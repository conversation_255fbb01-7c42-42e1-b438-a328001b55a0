<?php

namespace App\Http\Controllers\Vendor;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class DashboardController extends Controller
{
    /**
     * Display the vendor dashboard.
     *
     * @return \Illuminate\View\View
     */
    public function dashboard()
    {
        return view('vendor.dashboard');
    }

    public function analytics()
    {
        return view('vendor.analytics');
    }

    public function customers()
    {
        return view('vendor.customers');
    }

    public function discounts()
    {
        return view('vendor.discounts');
    }

    public function inventory()
    {
        return view('vendor.inventory');
    }

    public function marketing()
    {
        return view('vendor.marketing');
    }

    public function orders()
    {
        return view('vendor.orders');
    }

    public function products()
    {
        return view('vendor.products');
    }

    public function profile()
    {
        return view('vendor.profile');
    }

    public function reports()
    {
        return view('vendor.reports');
    }

    public function reviews()
    {
        return view('vendor.reviews');
    }

    public function settings()
    {
        return view('vendor.settings');
    }

    public function shipping()
    {
        return view('vendor.shipping');
    }

    public function staff()
    {
        return view('vendor.staff');
    }

    public function storeSettings()
    {
        return view('vendor.store-settings');
    }

    public function support()
    {
        return view('vendor.support');
    }

    public function chatFlowBuilder()
    {
        return view('vendor.chat-flow.builder');
    }

    public function chatFlowList()
    {
        return view('vendor.chat-flow.list');
    }
}
