@php
    // This block can be used for any server-side logic if needed in the future.
@endphp
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WhaMart Store</title>
    <style>
        /* Basic Reset & Fonts */
        body, html {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
            background-color: #f0f2f5;
            overflow: hidden; /* Full page experience */
            height: 100%;
        }

        /* Store Theme Container */
        .store-theme {
            display: flex;
            flex-direction: column;
            height: 100vh;
            width: 100%;
            max-width: 800px; /* Max-width for larger screens */
            margin: 0 auto;
            background-color: #e5ddd5;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }

        /* Loading & Error States */
        .store-theme-loading, .store-theme-error {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100vh;
            text-align: center;
        }
        .loading-spinner {
            border: 4px solid rgba(0, 0, 0, 0.1);
            border-left-color: #25d366;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        .loading-text, .preloader-tagline { 
            margin-top: 15px; 
            color: #54656f;
        }
        .error-content button {
            margin-top: 20px;
            padding: 8px 16px;
            background-color: #25d366;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
        }

        /* Chat Header */
        .chat-header {
            display: flex;
            align-items: center;
            padding: 12px;
            background-color: white;
            color: black;
            height: 64px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            position: relative;
            z-index: 10;
        }
        .chat-header .icon-button {
            background: none; border: none; cursor: pointer; padding: 2px; display: flex; align-items: center; justify-content: center; width: 28px; height: 28px; margin: 0 4px; color: #54656f;
        }
        .chat-header .store-info {
            display: flex; align-items: center; flex: 1; margin-left: 2px; height: 100%;
        }
        .chat-header .logo-container {
            width: 40px; height: 40px; border-radius: 50%; overflow: hidden; background-color: #f0f0f0; flex-shrink: 0; margin-right: 12px; display: flex; align-items: center; justify-content: center;
        }
        .chat-header .logo-container img {
            width: 100%; height: 100%; object-fit: cover;
        }
        .chat-header .store-name {
            font-weight: 600; font-size: 16px; color: #111b21;
        }
        .verification-badge {
            width: 16px; height: 16px; margin-left: 6px;
        }

        /* Chat Body */
        .chat-body {
            flex: 1;
            overflow-y: auto;
            padding: 16px;
            background-image: url('/chat-background.jpg');
            background-repeat: repeat;
            background-size: 300px auto;
            position: relative;
        }
        .chat-body-overlay {
            position: absolute; top: 0; left: 0; right: 0; bottom: 0; background-color: rgba(230, 222, 178, 0.15); pointer-events: none; z-index: 1;
        }
        .chat-content {
            position: relative; z-index: 2;
        }

        /* Chat Message */
        .chat-message {
            display: flex; margin-bottom: 8px;
        }
        .chat-message.visitor {
            justify-content: flex-end;
        }
        .message-bubble {
            max-width: 75%; padding: 8px 12px; border-radius: 8px; font-size: 14px; line-height: 1.4;
        }
        .message-bubble.store {
            background-color: white; border-top-left-radius: 0;
        }
        .message-bubble.visitor {
            background-color: #dcf8c6; border-top-right-radius: 0;
        }
        .message-time {
            font-size: 11px; color: #8696a0; text-align: right; margin-top: 4px;
        }

        /* Quick Reply Buttons */
        .quick-replies {
            display: flex; flex-wrap: wrap; justify-content: flex-end; margin-top: 8px;
        }
        .quick-reply-btn {
            background-color: #fff; border: 1px solid #00a884; color: #00a884; padding: 8px 12px; border-radius: 20px; font-size: 14px; cursor: pointer; margin: 4px;
        }

        /* Chat Input Bar */
        .chat-input-bar {
            display: flex; align-items: center; padding: 6px 8px; background-color: transparent;
        }
        .input-container {
            display: flex; align-items: center; background-color: white; border-radius: 20px; padding: 6px 10px; flex: 1; margin-right: 8px; box-shadow: 0 1px 2px rgba(0,0,0,0.1);
        }
        .input-container input {
            flex: 1; border: none; outline: none; background-color: transparent; font-size: 14px; padding: 0 6px; height: 28px;
        }
        .send-btn {
            width: 36px; height: 36px; border-radius: 50%; background-color: #00a884; color: white; border: none; cursor: pointer; display: flex; align-items: center; justify-content: center; box-shadow: 0 1px 2px rgba(0,0,0,0.2); flex-shrink: 0;
        }

    </style>
</head>
<body>

    <div id="store-container">
        <!-- Loading State -->
        <div id="loading-state" class="store-theme-loading">
            <img src="/WhaMart_Logo.png" alt="WhaMart Logo" style="width: 120px; margin-bottom: 20px;" />
            <div class="loading-spinner"></div>
            <div class="loading-text">Loading Store...</div>
            <div class="preloader-tagline">दुकान Online है ❤️</div>
        </div>

        <!-- Error State -->
        <div id="error-state" class="store-theme-error" style="display: none;">
            <div class="error-content">
                <img src="/WhaMart_Logo.png" alt="WhaMart Logo" style="width: 120px; margin-bottom: 20px;" />
                <h2 style="color: #DC3545; margin-top: 20px;">Error</h2>
                <p id="error-message" style="margin-top: 10px; text-align: center;"></p>
                <div class="preloader-tagline" style="margin: 15px 0">दुकान Online है ❤️</div>
                <button onclick="window.location.reload()">Try Again</button>
            </div>
        </div>

        <!-- Store Theme -->
        <div id="store-theme" class="store-theme" style="display: none;">
            <!-- Chat Header will be inserted here by JS -->
            <header id="chat-header-container"></header>
            
            <!-- Chat Body -->
            <div class="chat-body" id="chat-body">
                <div class="chat-body-overlay"></div>
                <div class="chat-content" id="chat-content">
                    <!-- Messages will be inserted here by JS -->
                </div>
                <div id="messages-end-ref"></div>
            </div>

            <!-- Chat Input Bar -->
            <footer id="chat-input-container"></footer>
        </div>
    </div>

    <script>
    document.addEventListener('DOMContentLoaded', function () {
        // Element references
        const storeTheme = document.getElementById('store-theme');
        const loadingState = document.getElementById('loading-state');
        const errorState = document.getElementById('error-state');
        const errorMessageEl = document.getElementById('error-message');
        const chatHeaderContainer = document.getElementById('chat-header-container');
        const chatContent = document.getElementById('chat-content');
        const chatInputContainer = document.getElementById('chat-input-container');
        const messagesEndRef = document.getElementById('messages-end-ref');

        // Data from Controller
        const storeData = JSON.parse('{!! addslashes($storeData) !!}');
        const chatFlowData = {!! $chatFlowData ?? 'null' !!}; // Use null if chatFlowData is not passed
        let messages = [];

        // --- UI Rendering Functions ---
        function renderChatHeader(store) {
            const logoUrl = store.logo_url || '/store-logo.png';
            chatHeaderContainer.innerHTML = `
                <div class="chat-header">
                    <button class="icon-button" onclick="history.back()"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" style="width:24px; height:24px;"><path stroke-linecap="round" stroke-linejoin="round" d="M15.75 19.5L8.25 12l7.5-7.5" /></svg></button>
                    <div class="store-info">
                        <div class="logo-container"><img src="${logoUrl}" alt="${store.name} Logo"/></div>
                        <div>
                            <span class="store-name">${store.name}</span>
                            ${store.is_verified ? '<img src="/verified-badge.svg" alt="Verified" class="verification-badge"/>' : ''}
                        </div>
                    </div>
                    <button class="icon-button"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" style="width:24px; height:24px;"><path stroke-linecap="round" stroke-linejoin="round" d="M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 002.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 01-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 00-1.091-.852H4.5A2.25 2.25 0 002.25 4.5v2.25z" /></svg></button>
                    <button class="icon-button"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" style="width:24px; height:24px;"><path stroke-linecap="round" stroke-linejoin="round" d="M12 6.75a.75.75 0 110-1.5.75.75 0 010 1.5zM12 12.75a.75.75 0 110-1.5.75.75 0 010 1.5zM12 18.75a.75.75 0 110-1.5.75.75 0 010 1.5z" /></svg></button>
                </div>
            `;
        }

        function renderChatInputBar() {
            chatInputContainer.innerHTML = `
                <div class="chat-input-bar">
                    <div class="input-container">
                        <input type="text" id="message-input" placeholder="Message"/>
                    </div>
                    <button id="send-button" class="send-btn"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" style="width:20px; height:20px;"><path d="M3.105 2.289a.75.75 0 00-.826.95l1.414 4.949a.75.75 0 00.95.826L11.25 9.25v1.5L4.643 11.96a.75.75 0 00-.95.826l-1.414 4.949a.75.75 0 00.826.95l14.438-5.359a.75.75 0 000-1.414L3.105 2.289z" /></svg></button>
                </div>
            `;
            document.getElementById('send-button').addEventListener('click', handleSendMessage);
            document.getElementById('message-input').addEventListener('keypress', (e) => {
                if (e.key === 'Enter') handleSendMessage();
            });
        }

        function renderMessages() {
            chatContent.innerHTML = messages.map(msg => {
                return `
                <div class="chat-message ${msg.sender}">
                    <div class="message-bubble ${msg.sender}">
                        <div>${msg.content}</div>
                        ${msg.buttons ? `<div class="quick-replies">${msg.buttons.map(btn => `<button class="quick-reply-btn" data-next-node-id="${btn.nextNodeId}">${btn.text}</button>`).join('')}</div>` : ''}
                        <div class="message-time">${new Date(msg.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}</div>
                    </div>
                </div>
            `}).join('');
            messagesEndRef.scrollIntoView({ behavior: 'smooth' });
        }

        // --- Event Handlers ---
        function handleSendMessage() {
            const input = document.getElementById('message-input');
            const content = input.value.trim();
            if (!content) return;

            const userMessage = {
                id: Date.now(),
                content: content,
                sender: 'visitor',
                timestamp: new Date(),
            };
            messages.push(userMessage);
            renderMessages();
            input.value = '';
            setTimeout(() => handleBotResponse(userMessage), 1000);
        }

        function handleQuickReply(e) {
            if (!e.target.classList.contains('quick-reply-btn')) return;
            const text = e.target.textContent;
            const nextNodeId = e.target.dataset.nextNodeId;

            const userMessage = {
                id: Date.now(),
                content: text,
                sender: 'visitor',
                timestamp: new Date(),
            };
            messages.push(userMessage);
            renderMessages();
            
            if (chatFlowData && nextNodeId) {
                const nextNode = chatFlowData.flow_data.nodes.find(node => node.id === nextNodeId);
                if (nextNode) {
                    setTimeout(() => {
                        const botMessage = createMessageFromNode(nextNode);
                        if (botMessage) {
                            messages.push(botMessage);
                            renderMessages();
                        }
                    }, 500);
                }
            }
        }
        chatContent.addEventListener('click', handleQuickReply);

        function handleBotResponse(userMessage) {
            const responseMessage = {
                id: Date.now(),
                content: "Thanks for your message! I'm a simple bot and can only respond to buttons for now.",
                sender: 'store',
                timestamp: new Date(),
            };
            messages.push(responseMessage);
            renderMessages();
        }

        function createMessageFromNode(node) {
            if (!node || !node.data || !node.data.content) return null;
            const message = {
                id: node.id,
                content: node.data.content,
                sender: 'store',
                timestamp: new Date(),
                nodeId: node.id
            };

            const outgoingEdges = chatFlowData.flow_data.edges.filter(edge => edge.source === node.id);
            if (outgoingEdges.length > 0) {
                message.buttons = outgoingEdges.map(edge => {
                    const targetNode = chatFlowData.flow_data.nodes.find(n => n.id === edge.target);
                    return {
                        text: edge.label || (targetNode && targetNode.data ? targetNode.data.buttonLabel : 'Option'),
                        nextNodeId: edge.target
                    };
                });
            }
            return message;
        }

        // --- Main Initialization Logic ---
        function initializeStore() {
            try {
                if (!storeData) {
                    throw new Error("Store data is not available.");
                }
                document.title = `${storeData.name} - WhaMart Store`;
                renderChatHeader(storeData);
                renderChatInputBar();

                if (chatFlowData && chatFlowData.flow_data && chatFlowData.flow_data.nodes) {
                    const startNode = chatFlowData.flow_data.nodes.find(node => node.type === 'startNode') || chatFlowData.flow_data.nodes[0];
                    if (startNode) {
                        const firstMessage = createMessageFromNode(startNode);
                        if (firstMessage) {
                            messages.push(firstMessage);
                            renderMessages();
                        }
                    }
                } else {
                    const noFlowMessage = {
                        id: 'no-flow',
                        content: `Welcome to ${storeData.name}! How can we help you today?`,
                        sender: 'store',
                        timestamp: new Date(),
                    };
                    messages.push(noFlowMessage);
                    renderMessages();
                }

                loadingState.style.display = 'none';
                storeTheme.style.display = 'flex';

            } catch (err) {
                console.error('Failed to initialize store:', err);
                errorMessageEl.textContent = 'Failed to initialize store. There might be an issue with the data or the store may not exist.';
                loadingState.style.display = 'none';
                errorState.style.display = 'flex';
            }
        }

        initializeStore();
    });
</script>
                }
            }

            initializeStore();
        });
    </script>
</body>
</html>
