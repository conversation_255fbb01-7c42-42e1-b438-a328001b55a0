<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" class="h-full">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <meta name="theme-color" content="#7ED957">

    <title>@yield('title', 'Vendor Dashboard') - {{ config('app.name', 'WhaMart') }}</title>
    <meta name="description" content="@yield('description', 'Modern vendor dashboard for WhaMart - Manage your store, products, orders, and analytics')">

    <!-- Preload Critical Resources -->
    <link rel="preload" href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" as="style">
    <link rel="preload" href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" as="style">

    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">

    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <link href="https://unpkg.com/boxicons@2.1.4/css/boxicons.min.css" rel="stylesheet">

    <!-- Modern Vendor Dashboard CSS -->
    <link rel="stylesheet" href="{{ asset('css/vendor/modern-vendor-base.css') }}">
    <link rel="stylesheet" href="{{ asset('css/vendor/modern-vendor-layout.css') }}">
    <link rel="stylesheet" href="{{ asset('css/vendor/modern-vendor-components.css') }}">
    <link rel="stylesheet" href="{{ asset('css/vendor/modern-vendor-utilities.css') }}">

    <!-- Page-specific CSS -->
    @stack('page-styles')

    <!-- Alpine.js -->
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>

    <!-- Chart Libraries -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>

    <!-- Animation Library -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>

    <!-- Modern Vendor Layout JavaScript -->
    <script src="{{ asset('js/modern-vendor-layout.js') }}" defer></script>

    <!-- Additional Scripts -->
    @stack('head-scripts')
</head>
<body class="modern-vendor-body"
      x-data="modernVendorApp()"
      x-init="initializeApp()"
      :class="{
          'sidebar-collapsed': sidebarCollapsed,
          'mobile-sidebar-open': mobileSidebarOpen,
          'dark-mode': darkMode
      }"
      @resize.window="handleResize()"
      @keydown.escape="closeMobileSidebar()">

    <!-- Loading Screen -->
    <div x-show="isLoading"
         x-transition:enter="transition ease-out duration-300"
         x-transition:enter-start="opacity-0"
         x-transition:enter-end="opacity-100"
         x-transition:leave="transition ease-in duration-300"
         x-transition:leave-start="opacity-100"
         x-transition:leave-end="opacity-0"
         class="modern-loading-screen">
        <div class="loading-content">
            <div class="loading-logo">
                <div class="logo-container">
                    <img src="/WhaMart_Logo.png" alt="WhaMart" class="logo-image" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                    <div class="logo-fallback" style="display: none;">
                        <i class="fas fa-store"></i>
                    </div>
                </div>
                <div class="loading-spinner">
                    <div class="spinner-ring"></div>
                    <div class="spinner-ring"></div>
                    <div class="spinner-ring"></div>
                </div>
            </div>
            <div class="loading-text">
                <h3>Loading your dashboard...</h3>
                <p>Preparing your vendor experience</p>
            </div>
        </div>
    </div>

    <!-- Mobile Sidebar Overlay -->
    <div x-show="mobileSidebarOpen"
         x-transition:enter="transition-opacity ease-linear duration-300"
         x-transition:enter-start="opacity-0"
         x-transition:enter-end="opacity-100"
         x-transition:leave="transition-opacity ease-linear duration-300"
         x-transition:leave-start="opacity-100"
         x-transition:leave-end="opacity-0"
         @click="closeMobileSidebar()"
         class="mobile-sidebar-overlay"></div>

    <!-- Main App Container -->
    <div class="modern-vendor-app">

        <!-- Modern Sidebar -->
        <aside class="modern-sidebar"
               :class="{ 'collapsed': sidebarCollapsed, 'mobile-open': mobileSidebarOpen }"
               x-transition:enter="transition ease-out duration-300"
               x-transition:enter-start="-translate-x-full"
               x-transition:enter-end="translate-x-0"
               x-transition:leave="transition ease-in duration-300"
               x-transition:leave-start="translate-x-0"
               x-transition:leave-end="-translate-x-full">

            <!-- Sidebar Header -->
            <div class="sidebar-header">
                <div class="brand-container" :class="{ 'collapsed': sidebarCollapsed }">
                    <div class="brand-logo">
                        <img src="/WhaMart_Logo.png" alt="WhaMart" class="logo-full" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                        <div class="logo-icon" style="display: none;">
                            <i class="fas fa-store"></i>
                        </div>
                    </div>
                    <div class="brand-text" x-show="!sidebarCollapsed">
                        <h2>WhaMart</h2>
                        <span>Vendor Dashboard</span>
                    </div>
                </div>

                <!-- Sidebar Toggle -->
                <button @click="toggleSidebar()" class="sidebar-toggle-btn">
                    <i class="fas fa-chevron-left" :class="{ 'rotate-180': sidebarCollapsed }"></i>
                </button>
            </div>

            <!-- Sidebar Navigation -->
            <nav class="sidebar-nav">
                <div class="nav-section">
                    <div class="nav-section-title" x-show="!sidebarCollapsed">
                        <span>Main</span>
                    </div>

                    <ul class="nav-menu">
                        <li class="nav-item">
                            <a href="{{ route('vendor.dashboard') }}"
                               class="nav-link {{ request()->routeIs('vendor.dashboard') ? 'active' : '' }}">
                                <div class="nav-icon">
                                    <i class="fas fa-home"></i>
                                </div>
                                <span class="nav-text" x-show="!sidebarCollapsed">Dashboard</span>
                                <div class="nav-indicator"></div>
                            </a>
                        </li>

                        <li class="nav-item">
                            <a href="{{ route('vendor.analytics') }}"
                               class="nav-link {{ request()->routeIs('vendor.analytics') ? 'active' : '' }}">
                                <div class="nav-icon">
                                    <i class="fas fa-chart-line"></i>
                                </div>
                                <span class="nav-text" x-show="!sidebarCollapsed">Analytics</span>
                                <div class="nav-indicator"></div>
                            </a>
                        </li>
                    </ul>
                </div>

                        <li class="nav-item">
                            <a href="{{ route('vendor.orders') }}"
                               class="nav-link {{ request()->routeIs('vendor.orders*') ? 'active' : '' }}">
                                <div class="nav-icon">
                                    <i class="fas fa-shopping-bag"></i>
                                </div>
                                <span class="nav-text" x-show="!sidebarCollapsed">Orders</span>
                                <div class="nav-badge" x-show="!sidebarCollapsed">
                                    <span>12</span>
                                </div>
                                <div class="nav-indicator"></div>
                            </a>
                        </li>

                        <li class="nav-item">
                            <a href="{{ route('vendor.products') }}"
                               class="nav-link {{ request()->routeIs('vendor.products*') ? 'active' : '' }}">
                                <div class="nav-icon">
                                    <i class="fas fa-box"></i>
                                </div>
                                <span class="nav-text" x-show="!sidebarCollapsed">Products</span>
                                <div class="nav-indicator"></div>
                            </a>
                        </li>

                        <li class="nav-item">
                            <a href="{{ route('vendor.customers') }}"
                               class="nav-link {{ request()->routeIs('vendor.customers*') ? 'active' : '' }}">
                                <div class="nav-icon">
                                    <i class="fas fa-users"></i>
                                </div>
                                <span class="nav-text" x-show="!sidebarCollapsed">Customers</span>
                                <div class="nav-indicator"></div>
                            </a>
                        </li>
                    </ul>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title" x-show="!sidebarCollapsed">
                        <span>Store Management</span>
                    </div>

                    <ul class="nav-menu">
                        <li class="nav-item">
                            <a href="{{ route('vendor.inventory') }}"
                               class="nav-link {{ request()->routeIs('vendor.inventory*') ? 'active' : '' }}">
                                <div class="nav-icon">
                                    <i class="fas fa-warehouse"></i>
                                </div>
                                <span class="nav-text" x-show="!sidebarCollapsed">Inventory</span>
                                <div class="nav-indicator"></div>
                            </a>
                        </li>

                        <li class="nav-item">
                            <a href="{{ route('vendor.discounts') }}"
                               class="nav-link {{ request()->routeIs('vendor.discounts*') ? 'active' : '' }}">
                                <div class="nav-icon">
                                    <i class="fas fa-percent"></i>
                                </div>
                                <span class="nav-text" x-show="!sidebarCollapsed">Discounts</span>
                                <div class="nav-indicator"></div>
                            </a>
                        </li>

                        <li class="nav-item">
                            <a href="{{ route('vendor.reviews') }}"
                               class="nav-link {{ request()->routeIs('vendor.reviews*') ? 'active' : '' }}">
                                <div class="nav-icon">
                                    <i class="fas fa-star"></i>
                                </div>
                                <span class="nav-text" x-show="!sidebarCollapsed">Reviews</span>
                                <div class="nav-indicator"></div>
                            </a>
                        </li>

                        <li class="nav-item">
                            <a href="{{ route('vendor.shipping') }}"
                               class="nav-link {{ request()->routeIs('vendor.shipping*') ? 'active' : '' }}">
                                <div class="nav-icon">
                                    <i class="fas fa-truck"></i>
                                </div>
                                <span class="nav-text" x-show="!sidebarCollapsed">Shipping</span>
                                <div class="nav-indicator"></div>
                            </a>
                        </li>
                    </ul>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title" x-show="!sidebarCollapsed">
                        <span>Growth & Marketing</span>
                    </div>

                    <ul class="nav-menu">
                        <li class="nav-item">
                            <a href="{{ route('vendor.marketing') }}"
                               class="nav-link {{ request()->routeIs('vendor.marketing*') ? 'active' : '' }}">
                                <div class="nav-icon">
                                    <i class="fas fa-bullhorn"></i>
                                </div>
                                <span class="nav-text" x-show="!sidebarCollapsed">Marketing</span>
                                <div class="nav-indicator"></div>
                            </a>
                        </li>

                        <li class="nav-item">
                            <a href="{{ route('vendor.reports') }}"
                               class="nav-link {{ request()->routeIs('vendor.reports*') ? 'active' : '' }}">
                                <div class="nav-icon">
                                    <i class="fas fa-file-chart-line"></i>
                                </div>
                                <span class="nav-text" x-show="!sidebarCollapsed">Reports</span>
                                <div class="nav-indicator"></div>
                            </a>
                        </li>

                        <li class="nav-item">
                            <a href="{{ route('vendor.chat-flow.list') }}"
                               class="nav-link {{ request()->routeIs('vendor.chat-flow*') ? 'active' : '' }}">
                                <div class="nav-icon">
                                    <i class="fab fa-whatsapp"></i>
                                </div>
                                <span class="nav-text" x-show="!sidebarCollapsed">Chat Flows</span>
                                <div class="nav-indicator"></div>
                            </a>
                        </li>
                    </ul>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title" x-show="!sidebarCollapsed">
                        <span>Settings</span>
                    </div>

                    <ul class="nav-menu">
                        <li class="nav-item">
                            <a href="{{ route('vendor.store-settings') }}"
                               class="nav-link {{ request()->routeIs('vendor.store-settings*') ? 'active' : '' }}">
                                <div class="nav-icon">
                                    <i class="fas fa-store-alt"></i>
                                </div>
                                <span class="nav-text" x-show="!sidebarCollapsed">Store Settings</span>
                                <div class="nav-indicator"></div>
                            </a>
                        </li>

                        <li class="nav-item">
                            <a href="{{ route('vendor.profile') }}"
                               class="nav-link {{ request()->routeIs('vendor.profile*') ? 'active' : '' }}">
                                <div class="nav-icon">
                                    <i class="fas fa-user-cog"></i>
                                </div>
                                <span class="nav-text" x-show="!sidebarCollapsed">Profile</span>
                                <div class="nav-indicator"></div>
                            </a>
                        </li>

                        <li class="nav-item">
                            <a href="{{ route('vendor.support') }}"
                               class="nav-link {{ request()->routeIs('vendor.support*') ? 'active' : '' }}">
                                <div class="nav-icon">
                                    <i class="fas fa-life-ring"></i>
                                </div>
                                <span class="nav-text" x-show="!sidebarCollapsed">Support</span>
                                <div class="nav-indicator"></div>
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Sidebar Footer -->
            <div class="sidebar-footer">
                <div class="upgrade-card" x-show="!sidebarCollapsed">
                    <div class="upgrade-icon">
                        <i class="fas fa-crown"></i>
                    </div>
                    <div class="upgrade-content">
                        <h4>Upgrade to Pro</h4>
                        <p>Unlock advanced features</p>
                        <button class="upgrade-btn">
                            <span>Upgrade Now</span>
                            <i class="fas fa-arrow-right"></i>
                        </button>
                    </div>
                </div>
            </div>
        </aside>

        <!-- Main Content Wrapper -->
        <div class="modern-main-wrapper">
            <!-- Modern Header -->
            <header class="modern-header">
                <div class="header-content">
                    <!-- Left Section -->
                    <div class="header-left">
                        <!-- Mobile Menu Toggle -->
                        <button @click="toggleMobileSidebar()" class="mobile-menu-btn">
                            <i class="fas fa-bars"></i>
                        </button>

                        <!-- Page Title -->
                        <div class="page-title-section">
                            <h1 class="page-title">@yield('page-title', 'Dashboard')</h1>
                            <nav class="breadcrumb">
                                <span class="breadcrumb-item">
                                    <i class="fas fa-home"></i>
                                    <span>Home</span>
                                </span>
                                <span class="breadcrumb-separator">
                                    <i class="fas fa-chevron-right"></i>
                                </span>
                                <span class="breadcrumb-item current">
                                    @yield('page-title', 'Dashboard')
                                </span>
                            </nav>
                        </div>
                    </div>

                    <!-- Right Section -->
                    <div class="header-right">
                        <!-- Quick Actions -->
                        <div class="quick-actions">
                            <button class="quick-action-btn" title="Add New Product">
                                <i class="fas fa-plus"></i>
                            </button>

                            <button class="quick-action-btn" title="Notifications">
                                <i class="fas fa-bell"></i>
                                <span class="action-badge">3</span>
                            </button>
                        </div>

                        <!-- User Profile -->
                        <div class="user-profile-container" x-data="{ profileOpen: false }">
                            <button @click="profileOpen = !profileOpen" class="user-profile-btn">
                                <div class="user-avatar-wrapper">
                                    <img src="https://ui-avatars.com/api/?name={{ urlencode(auth()->user()->name) }}&background=7ED957&color=fff"
                                         alt="User avatar" class="user-avatar-img">
                                    <div class="user-status-dot"></div>
                                </div>
                                <div class="user-info">
                                    <span class="user-name">{{ auth()->user()->name }}</span>
                                    <span class="user-role">Vendor</span>
                                </div>
                                <div class="user-dropdown-arrow">
                                    <i class="fas fa-chevron-down" :class="{ 'rotate-180': profileOpen }"></i>
                                </div>
                            </button>

                            <!-- User Profile Dropdown -->
                            <div x-show="profileOpen"
                                 @click.away="profileOpen = false"
                                 x-transition:enter="transition ease-out duration-200"
                                 x-transition:enter-start="opacity-0 transform scale-95"
                                 x-transition:enter-end="opacity-100 transform scale-100"
                                 x-transition:leave="transition ease-in duration-150"
                                 x-transition:leave-start="opacity-100 transform scale-100"
                                 x-transition:leave-end="opacity-0 transform scale-95"
                                 class="user-profile-dropdown">

                                <div class="profile-dropdown-header">
                                    <div class="profile-avatar">
                                        <img src="https://ui-avatars.com/api/?name={{ urlencode(auth()->user()->name) }}&background=7ED957&color=fff"
                                             alt="User avatar">
                                    </div>
                                    <div class="profile-info">
                                        <h4 class="profile-name">{{ auth()->user()->name }}</h4>
                                        <p class="profile-email">{{ auth()->user()->email }}</p>
                                        <span class="profile-badge">Vendor Account</span>
                                    </div>
                                </div>

                                <div class="profile-dropdown-menu">
                                    <a href="{{ route('vendor.profile') }}" class="profile-menu-item">
                                        <i class="fas fa-user"></i>
                                        <span>My Profile</span>
                                    </a>
                                    <a href="{{ route('vendor.settings') }}" class="profile-menu-item">
                                        <i class="fas fa-cog"></i>
                                        <span>Settings</span>
                                    </a>

                                    <div class="profile-menu-divider"></div>

                                    <form method="POST" action="{{ route('logout') }}" class="profile-logout-form">
                                        @csrf
                                        <button type="submit" class="profile-menu-item logout-item">
                                            <i class="fas fa-sign-out-alt"></i>
                                            <span>Sign Out</span>
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Main Content -->
            <main class="modern-main">
                <!-- Page Content -->
                <div class="page-content">
                    @yield('content')
                </div>
            </main>
        </div>
    </div>

    <!-- Global Modals -->
    <div id="global-modals">
        @stack('modals')
    </div>

    <!-- Page Scripts -->
    @stack('scripts')

    <!-- Modern Vendor App JavaScript -->
    <script>
    function modernVendorApp() {
        return {
            // State Management
            isLoading: true,
            darkMode: localStorage.getItem('vendor-dark-mode') === 'true',
            sidebarCollapsed: localStorage.getItem('vendor-sidebar-collapsed') === 'true',
            mobileSidebarOpen: false,

            // Initialization
            initializeApp() {
                // Apply theme
                this.applyTheme();

                // Hide loading screen after initialization
                setTimeout(() => {
                    this.isLoading = false;
                }, 1000);

                // Handle window resize
                this.handleResize();

                // Initialize animations
                this.initAnimations();
            },

            // Theme Management
            toggleDarkMode() {
                this.darkMode = !this.darkMode;
                localStorage.setItem('vendor-dark-mode', this.darkMode);
                this.applyTheme();
            },

            applyTheme() {
                if (this.darkMode) {
                    document.documentElement.classList.add('dark');
                    document.body.classList.add('dark-mode');
                } else {
                    document.documentElement.classList.remove('dark');
                    document.body.classList.remove('dark-mode');
                }
            },

            // Sidebar Management
            toggleSidebar() {
                this.sidebarCollapsed = !this.sidebarCollapsed;
                localStorage.setItem('vendor-sidebar-collapsed', this.sidebarCollapsed);
            },

            toggleMobileSidebar() {
                this.mobileSidebarOpen = !this.mobileSidebarOpen;
            },

            closeMobileSidebar() {
                this.mobileSidebarOpen = false;
            },

            // Window Resize Handler
            handleResize() {
                if (window.innerWidth > 1024) {
                    this.mobileSidebarOpen = false;
                }
            },

            // Animations
            initAnimations() {
                // Animate sidebar items on load
                if (typeof gsap !== 'undefined') {
                    gsap.from('.nav-item', {
                        duration: 0.6,
                        y: 20,
                        opacity: 0,
                        stagger: 0.1,
                        ease: 'power2.out',
                        delay: 0.5
                    });
                }
            }
        }
    }
    </script>

</body>
</html>