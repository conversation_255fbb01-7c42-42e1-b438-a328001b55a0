@extends('layouts.vendor')

@section('title', 'Analytics')

@push('page-styles')
<link rel="stylesheet" href="{{ asset('css/vendor/pages/analytics.css') }}">
@endpush

@section('content')
<div x-data="vendorAnalytics()">
    <!-- Analytics Header -->
    <div class="analytics-header">
        <div class="header-content">
            <div class="header-text">
                <h1 class="analytics-title">Store Analytics</h1>
                <p class="analytics-subtitle">Track your performance and grow your business with data-driven insights</p>

                <div class="date-range-selector">
                    <button class="date-range-btn active" @click="setTimeRange('day')">Today</button>
                    <button class="date-range-btn" @click="setTimeRange('week')">7 Days</button>
                    <button class="date-range-btn" @click="setTimeRange('month')">30 Days</button>
                    <button class="date-range-btn" @click="setTimeRange('quarter')">90 Days</button>
                    <button class="date-range-btn" @click="setTimeRange('year')">1 Year</button>
                </div>
            </div>

            <div class="header-actions">
                <button class="btn btn-secondary">
                    <i class="fas fa-download"></i>
                    <span>Export Report</span>
                </button>
                <button class="btn btn-primary">
                    <i class="fas fa-cog"></i>
                    <span>Configure</span>
                </button>
            </div>
        </div>
    </div>

    <!-- Loading State -->
    <template x-if="loading">
        <div class="d-flex justify-center items-center" style="min-height: 400px;">
            <div class="loading-spinner"></div>
        </div>
    </template>

    <!-- KPI Grid -->
    <div class="kpi-grid" x-show="!loading">
        <!-- Revenue KPI -->
        <div class="kpi-card revenue">
            <div class="kpi-header">
                <div class="kpi-icon">
                    <i class="fas fa-rupee-sign"></i>
                </div>
                <button class="kpi-menu">
                    <i class="fas fa-ellipsis-h"></i>
                </button>
            </div>
            <div class="kpi-content">
                <div class="kpi-label">Total Revenue</div>
                <div class="kpi-value" x-text="formatCurrency(analyticsData?.revenue || 0)">₹2,45,680</div>
                <div class="kpi-change positive">
                    <i class="fas fa-arrow-up"></i>
                    <span x-text="analyticsData?.revenueChange || '+15.2%'">+15.2% from last period</span>
                </div>
            </div>
            <div class="kpi-sparkline">
                <canvas id="revenueSparkline" width="200" height="40"></canvas>
            </div>
        </div>

        <!-- Orders KPI -->
        <div class="kpi-card orders">
            <div class="kpi-header">
                <div class="kpi-icon">
                    <i class="fas fa-shopping-cart"></i>
                </div>
                <button class="kpi-menu">
                    <i class="fas fa-ellipsis-h"></i>
                </button>
            </div>
            <div class="kpi-content">
                <div class="kpi-label">Total Orders</div>
                <div class="kpi-value" x-text="analyticsData?.orders || 0">1,234</div>
                <div class="kpi-change positive">
                    <i class="fas fa-arrow-up"></i>
                    <span x-text="analyticsData?.ordersChange || '+8.5%'">+8.5% from last period</span>
                </div>
            </div>
            <div class="kpi-sparkline">
                <canvas id="ordersSparkline" width="200" height="40"></canvas>
            </div>
        </div>

        <!-- Customers KPI -->
        <div class="kpi-card customers">
            <div class="kpi-header">
                <div class="kpi-icon">
                    <i class="fas fa-users"></i>
                </div>
                <button class="kpi-menu">
                    <i class="fas fa-ellipsis-h"></i>
                </button>
            </div>
            <div class="kpi-content">
                <div class="kpi-label">New Customers</div>
                <div class="kpi-value" x-text="analyticsData?.customers || 0">89</div>
                <div class="kpi-change positive">
                    <i class="fas fa-arrow-up"></i>
                    <span x-text="analyticsData?.customersChange || '+12.3%'">+12.3% from last period</span>
                </div>
            </div>
            <div class="kpi-sparkline">
                <canvas id="customersSparkline" width="200" height="40"></canvas>
            </div>
        </div>

        <!-- Conversion Rate KPI -->
        <div class="kpi-card conversion">
            <div class="kpi-header">
                <div class="kpi-icon">
                    <i class="fas fa-percentage"></i>
                </div>
                <button class="kpi-menu">
                    <i class="fas fa-ellipsis-h"></i>
                </button>
            </div>
            <div class="kpi-content">
                <div class="kpi-label">Conversion Rate</div>
                <div class="kpi-value" x-text="(analyticsData?.conversionRate || 0) + '%'">3.2%</div>
                <div class="kpi-change negative">
                    <i class="fas fa-arrow-down"></i>
                    <span x-text="analyticsData?.conversionChange || '-0.5%'">-0.5% from last period</span>
                </div>
            </div>
            <div class="kpi-sparkline">
                <canvas id="conversionSparkline" width="200" height="40"></canvas>
            </div>
        </div>
    </div>

    <!-- Chart Grid -->
    <div class="chart-grid">
        <!-- Revenue Chart -->
        <div class="analytics-chart-card">
            <div class="chart-header">
                <h3 class="chart-title">Revenue Trends</h3>
                <div class="chart-controls">
                    <button class="chart-control active">Revenue</button>
                    <button class="chart-control">Orders</button>
                    <button class="chart-control">Customers</button>
                </div>
            </div>
            <div class="chart-body">
                <canvas id="revenueChart" width="600" height="300"></canvas>
            </div>
        </div>

        <!-- Top Products -->
        <div class="analytics-chart-card">
            <div class="chart-header">
                <h3 class="chart-title">Top Performing Products</h3>
                <a href="{{ route('vendor.products.index') }}" class="stat-link">View all products</a>
            </div>
            <div class="chart-body">
                <div class="top-products-list">
                    <div class="product-item">
                        <div class="product-rank">1</div>
                        <div class="product-image">
                            <i class="fas fa-headphones"></i>
                        </div>
                        <div class="product-info">
                            <div class="product-name">Premium Wireless Headphones</div>
                            <div class="product-sales">245 sales this month</div>
                        </div>
                        <div class="product-revenue">₹48,900</div>
                    </div>

                    <div class="product-item">
                        <div class="product-rank">2</div>
                        <div class="product-image">
                            <i class="fas fa-laptop"></i>
                        </div>
                        <div class="product-info">
                            <div class="product-name">Gaming Laptop Pro</div>
                            <div class="product-sales">89 sales this month</div>
                        </div>
                        <div class="product-revenue">₹89,100</div>
                    </div>

                    <div class="product-item">
                        <div class="product-rank">3</div>
                        <div class="product-image">
                            <i class="fas fa-mobile-alt"></i>
                        </div>
                        <div class="product-info">
                            <div class="product-name">Smartphone Case</div>
                            <div class="product-sales">156 sales this month</div>
                        </div>
                        <div class="product-revenue">₹15,600</div>
                    </div>

                    <div class="product-item">
                        <div class="product-rank">4</div>
                        <div class="product-image">
                            <i class="fas fa-watch"></i>
                        </div>
                        <div class="product-info">
                            <div class="product-name">Smart Watch</div>
                            <div class="product-sales">67 sales this month</div>
                        </div>
                        <div class="product-revenue">₹20,100</div>
                    </div>

                    <div class="product-item">
                        <div class="product-rank">5</div>
                        <div class="product-image">
                            <i class="fas fa-camera"></i>
                        </div>
                        <div class="product-info">
                            <div class="product-name">Digital Camera</div>
                            <div class="product-sales">34 sales this month</div>
                        </div>
                        <div class="product-revenue">₹34,000</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
        </div>
    </div>

    <!-- Analytics Tables Section -->
    <div class="analytics-table-section">
        <!-- Traffic Sources -->
        <div class="analytics-table-card">
            <div class="table-header">
                <h3 class="table-title">Traffic Sources</h3>
                <a href="#" class="stat-link">View details</a>
            </div>
            <div class="table-body">
                <table class="analytics-table">
                    <thead>
                        <tr>
                            <th>Source</th>
                            <th>Visitors</th>
                            <th>Conversion</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Direct</td>
                            <td>2,456</td>
                            <td>4.2%</td>
                        </tr>
                        <tr>
                            <td>Google Search</td>
                            <td>1,890</td>
                            <td>3.8%</td>
                        </tr>
                        <tr>
                            <td>Social Media</td>
                            <td>1,234</td>
                            <td>2.9%</td>
                        </tr>
                        <tr>
                            <td>Email</td>
                            <td>890</td>
                            <td>5.1%</td>
                        </tr>
                        <tr>
                            <td>Referral</td>
                            <td>567</td>
                            <td>3.2%</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Customer Demographics -->
        <div class="analytics-table-card">
            <div class="table-header">
                <h3 class="table-title">Customer Demographics</h3>
                <a href="#" class="stat-link">View details</a>
            </div>
            <div class="table-body">
                <table class="analytics-table">
                    <thead>
                        <tr>
                            <th>Age Group</th>
                            <th>Customers</th>
                            <th>Revenue</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>18-24</td>
                            <td>234</td>
                            <td>₹45,600</td>
                        </tr>
                        <tr>
                            <td>25-34</td>
                            <td>456</td>
                            <td>₹89,200</td>
                        </tr>
                        <tr>
                            <td>35-44</td>
                            <td>345</td>
                            <td>₹78,900</td>
                        </tr>
                        <tr>
                            <td>45-54</td>
                            <td>234</td>
                            <td>₹56,700</td>
                        </tr>
                        <tr>
                            <td>55+</td>
                            <td>123</td>
                            <td>₹34,500</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Export Section -->
    <div class="export-section">
        <h3 class="export-title">Export Analytics Data</h3>
        <p class="export-description">Download your analytics data in various formats for further analysis</p>
        <div class="export-actions">
            <button class="btn btn-secondary">
                <i class="fas fa-file-csv"></i>
                <span>Export CSV</span>
            </button>
            <button class="btn btn-secondary">
                <i class="fas fa-file-pdf"></i>
                <span>Export PDF</span>
            </button>
            <button class="btn btn-primary">
                <i class="fas fa-file-excel"></i>
                <span>Export Excel</span>
            </button>
        </div>
    </div>
</div>

@push('scripts')
<script>
function vendorAnalytics() {
    return {
        loading: true,
        timeRange: 'month',
        analyticsData: {
            revenue: 245680,
            revenueChange: '+15.2%',
            orders: 1234,
            ordersChange: '+8.5%',
            customers: 89,
            customersChange: '+12.3%',
            conversionRate: 3.2,
            conversionChange: '-0.5%'
        },
        revenueChart: null,

        init() {
            this.loading = false;
            this.$nextTick(() => {
                this.renderRevenueChart();
                this.renderSparklines();
            });
        },

        setTimeRange(range) {
            this.timeRange = range;
            // Update active button
            document.querySelectorAll('.date-range-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            this.fetchAnalyticsData();
        },

        fetchAnalyticsData() {
            this.loading = true;
            // Simulate API call
            setTimeout(() => {
                this.loading = false;
                this.$nextTick(() => {
                    this.renderRevenueChart();
                    this.renderSparklines();
                });
            }, 500);
        },

        formatCurrency(amount) {
            return new Intl.NumberFormat('en-IN', {
                style: 'currency',
                currency: 'INR',
                minimumFractionDigits: 0
            }).format(amount);
        },

        renderRevenueChart() {
            const ctx = document.getElementById('revenueChart');
            if (!ctx) return;

            if (this.revenueChart) {
                this.revenueChart.destroy();
            }

            this.revenueChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
                    datasets: [{
                        label: 'Revenue',
                        data: [120000, 150000, 180000, 160000, 200000, 220000, 250000, 230000, 260000, 280000, 300000, 320000],
                        borderColor: 'rgb(99, 102, 241)',
                        backgroundColor: 'rgba(99, 102, 241, 0.1)',
                        fill: true,
                        tension: 0.4,
                        borderWidth: 3
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: 'rgba(0, 0, 0, 0.05)'
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            });
        },

        renderSparklines() {
            // Render sparkline charts for KPIs
            this.renderSparkline('revenueSparkline', [20, 25, 30, 28, 35, 40, 45]);
            this.renderSparkline('ordersSparkline', [15, 18, 22, 20, 25, 28, 30]);
            this.renderSparkline('customersSparkline', [5, 8, 12, 10, 15, 18, 20]);
            this.renderSparkline('conversionSparkline', [3.1, 3.3, 3.5, 3.2, 3.0, 3.1, 3.2]);
        },

        renderSparkline(canvasId, data) {
            const ctx = document.getElementById(canvasId);
            if (!ctx) return;

            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: Array(data.length).fill(''),
                    datasets: [{
                        data: data,
                        borderColor: 'rgba(99, 102, 241, 0.8)',
                        backgroundColor: 'rgba(99, 102, 241, 0.1)',
                        fill: true,
                        tension: 0.4,
                        borderWidth: 2,
                        pointRadius: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: { display: false }
                    },
                    scales: {
                        x: { display: false },
                        y: { display: false }
                    },
                    elements: {
                        point: { radius: 0 }
                    }
                }
            });
        }
    }
}
</script>
@endpush

@endsection

