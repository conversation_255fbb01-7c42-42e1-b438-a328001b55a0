@extends('layouts.vendor')

@section('title', 'Customers')

@push('page-styles')
<link rel="stylesheet" href="{{ asset('css/vendor/pages/customers.css') }}">
@endpush

@section('content')
<div x-data="vendorCustomers()">
    <!-- Customers Header -->
    <div class="customers-header">
        <div class="customers-header-left">
            <h1 class="customers-title">Customer Management</h1>
            <p class="customers-subtitle">Manage your customer relationships and track their engagement</p>
        </div>
        <div class="customers-header-right">
            <button class="btn btn-secondary">
                <i class="fas fa-download"></i>
                <span>Export</span>
            </button>
            <button class="btn btn-primary">
                <i class="fas fa-user-plus"></i>
                <span>Add Customer</span>
            </button>
        </div>
    </div>

    <!-- Loading State -->
    <template x-if="loading">
        <div class="d-flex justify-center items-center" style="min-height: 400px;">
            <div class="loading-spinner"></div>
        </div>
    </template>

    <!-- Customer Stats -->
    <div class="customers-stats" x-show="!loading">
        <div class="customers-stat-card">
            <div class="stat-icon total">
                <i class="fas fa-users"></i>
            </div>
            <div class="stat-value" x-text="stats.totalCustomers">248</div>
            <div class="stat-label">Total Customers</div>
        </div>

        <div class="customers-stat-card">
            <div class="stat-icon new">
                <i class="fas fa-user-plus"></i>
            </div>
            <div class="stat-value" x-text="stats.newCustomers">12</div>
            <div class="stat-label">New This Month</div>
        </div>

        <div class="customers-stat-card">
            <div class="stat-icon active">
                <i class="fas fa-user-check"></i>
            </div>
            <div class="stat-value" x-text="stats.activeCustomers">186</div>
            <div class="stat-label">Active Customers</div>
        </div>

        <div class="customers-stat-card">
            <div class="stat-icon returning">
                <i class="fas fa-redo"></i>
            </div>
            <div class="stat-value" x-text="stats.returningCustomers">92</div>
            <div class="stat-label">Returning Customers</div>
        </div>
    </div>

    <!-- Filters -->
    <div class="customers-filters" x-show="!loading">
        <div class="filters-grid">
            <div class="filter-group">
                <label class="filter-label">Search Customers</label>
                <div class="search-input-container">
                    <i class="search-icon fas fa-search"></i>
                    <input type="text" x-model="searchTerm" placeholder="Search by name, email, or phone..." class="filter-input search-input">
                </div>
            </div>

            <div class="filter-group">
                <label class="filter-label">Status</label>
                <select x-model="statusFilter" class="filter-select">
                    <option value="">All Status</option>
                    <option value="active">Active</option>
                    <option value="inactive">Inactive</option>
                </select>
            </div>

            <div class="filter-group">
                <label class="filter-label">Customer Type</label>
                <select x-model="typeFilter" class="filter-select">
                    <option value="">All Types</option>
                    <option value="new">New</option>
                    <option value="returning">Returning</option>
                    <option value="vip">VIP</option>
                </select>
            </div>

            <div class="filter-group">
                <label class="filter-label">Spending</label>
                <select x-model="spendingFilter" class="filter-select">
                    <option value="">All Spending</option>
                    <option value="low">Under ₹5,000</option>
                    <option value="medium">₹5,000 - ₹25,000</option>
                    <option value="high">Above ₹25,000</option>
                </select>
            </div>

            <div class="filter-actions">
                <button @click="clearFilters()" class="btn btn-ghost">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
    </div>
    <!-- Customers Grid -->
    <div class="customers-grid" x-show="!loading && filteredCustomers.length > 0">
        <template x-for="customer in filteredCustomers" :key="customer.id">
            <div class="customer-card">
                <!-- Customer Actions -->
                <div class="customer-actions">
                    <button class="customer-action-btn" title="Edit Customer">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="customer-action-btn" title="Send Message">
                        <i class="fas fa-envelope"></i>
                    </button>
                    <button class="customer-action-btn" title="View Orders">
                        <i class="fas fa-shopping-bag"></i>
                    </button>
                </div>

                <!-- Customer Header -->
                <div class="customer-header">
                    <div class="customer-avatar">
                        <span x-text="customer.name.charAt(0).toUpperCase()"></span>
                        <div class="customer-status-dot" :class="customer.status"></div>
                    </div>
                    <div class="customer-info">
                        <div class="customer-name" x-text="customer.name"></div>
                        <div class="customer-email" x-text="customer.email"></div>
                        <div class="customer-joined" x-text="'Joined ' + customer.joinedDate"></div>
                    </div>
                </div>

                <!-- Customer Stats -->
                <div class="customer-stats">
                    <div class="customer-stat">
                        <div class="customer-stat-value" x-text="customer.totalOrders">12</div>
                        <div class="customer-stat-label">Orders</div>
                    </div>
                    <div class="customer-stat">
                        <div class="customer-stat-value" x-text="'₹' + customer.totalSpent.toLocaleString()">₹25,400</div>
                        <div class="customer-stat-label">Spent</div>
                    </div>
                    <div class="customer-stat">
                        <div class="customer-stat-value" x-text="customer.avgOrderValue">₹2,117</div>
                        <div class="customer-stat-label">Avg Order</div>
                    </div>
                </div>

                <!-- Customer Tags -->
                <div class="customer-tags">
                    <template x-for="tag in customer.tags" :key="tag">
                        <span class="customer-tag" :class="tag.toLowerCase()" x-text="tag"></span>
                    </template>
                </div>

                <!-- Customer Footer -->
                <div class="customer-footer">
                    <a :href="'/vendor/customers/' + customer.id" class="customer-btn">
                        <i class="fas fa-eye"></i>
                        <span>View Profile</span>
                    </a>
                    <button @click="contactCustomer(customer)" class="customer-btn primary">
                        <i class="fas fa-phone"></i>
                        <span>Contact</span>
                    </button>
                </div>
            </div>
        </template>
    </div>

    <!-- Empty State -->
    <div x-show="!loading && filteredCustomers.length === 0" class="customers-empty">
        <div class="empty-icon">
            <i class="fas fa-users"></i>
        </div>
        <h3 class="empty-title">No customers found</h3>
        <p class="empty-description">
            <span x-show="searchTerm || statusFilter || typeFilter || spendingFilter">
                Try adjusting your filters to find what you're looking for.
            </span>
            <span x-show="!searchTerm && !statusFilter && !typeFilter && !spendingFilter">
                Customers will appear here once they start purchasing from your store.
            </span>
        </p>
    </div>
</div>

@push('scripts')
<script>
function vendorCustomers() {
    return {
        loading: true,
        customers: [],
        stats: {
            totalCustomers: 248,
            newCustomers: 12,
            activeCustomers: 186,
            returningCustomers: 92
        },
        searchTerm: '',
        statusFilter: '',
        typeFilter: '',
        spendingFilter: '',

        init() {
            this.fetchData();
        },

        fetchData() {
            this.loading = true;
            setTimeout(() => {
                const names = [
                    'Rajesh Kumar', 'Priya Sharma', 'Amit Singh', 'Sunita Patel', 'Vikram Gupta',
                    'Anita Verma', 'Rohit Agarwal', 'Kavita Joshi', 'Suresh Reddy', 'Meera Nair',
                    'Deepak Mehta', 'Pooja Bansal', 'Ravi Tiwari', 'Sita Yadav', 'Manoj Saxena'
                ];

                const statuses = ['active', 'inactive'];
                const types = ['new', 'returning', 'vip'];

                this.customers = Array.from({ length: 50 }, (_, i) => {
                    const name = names[i % names.length];
                    const email = name.toLowerCase().replace(' ', '.') + '@example.com';
                    const status = statuses[Math.floor(Math.random() * statuses.length)];
                    const type = types[Math.floor(Math.random() * types.length)];
                    const totalSpent = Math.floor(1000 + Math.random() * 50000);
                    const totalOrders = Math.floor(1 + Math.random() * 20);
                    const avgOrderValue = Math.floor(totalSpent / totalOrders);

                    const customerTags = [];
                    if (type === 'vip') customerTags.push('VIP');
                    if (type === 'new') customerTags.push('New');
                    if (totalSpent > 25000) customerTags.push('High Value');
                    if (totalOrders > 10) customerTags.push('Frequent Buyer');

                    return {
                        id: i + 1,
                        name: name,
                        email: email,
                        status: status,
                        type: type,
                        totalSpent: totalSpent,
                        totalOrders: totalOrders,
                        avgOrderValue: avgOrderValue,
                        joinedDate: new Date(2023, Math.floor(Math.random() * 12), Math.floor(Math.random() * 28) + 1).toLocaleDateString('en-IN'),
                        tags: customerTags
                    };
                });
                this.loading = false;
            }, 1000);
        },

        get filteredCustomers() {
            return this.customers.filter(customer => {
                const searchMatch = !this.searchTerm ||
                    customer.name.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
                    customer.email.toLowerCase().includes(this.searchTerm.toLowerCase());

                const statusMatch = !this.statusFilter || customer.status === this.statusFilter;
                const typeMatch = !this.typeFilter || customer.type === this.typeFilter;

                let spendingMatch = true;
                if (this.spendingFilter === 'low') spendingMatch = customer.totalSpent < 5000;
                else if (this.spendingFilter === 'medium') spendingMatch = customer.totalSpent >= 5000 && customer.totalSpent <= 25000;
                else if (this.spendingFilter === 'high') spendingMatch = customer.totalSpent > 25000;

                return searchMatch && statusMatch && typeMatch && spendingMatch;
            });
        },

        clearFilters() {
            this.searchTerm = '';
            this.statusFilter = '';
            this.typeFilter = '';
            this.spendingFilter = '';
        },

        contactCustomer(customer) {
            // Contact customer functionality
            window.showToast(`Contacting ${customer.name}`, 'info');
        }
    }
}
</script>
@endpush
@endsection

