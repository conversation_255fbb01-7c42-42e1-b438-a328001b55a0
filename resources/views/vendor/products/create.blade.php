@extends('layouts.vendor')

@section('content')
<div x-data="addProductForm()" class="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="flex items-center mb-6">
        <a href="{{ route('vendor.products.index') }}" class="text-gray-600 hover:text-gray-900 mr-4">
            <i class="fas fa-arrow-left"></i>
        </a>
        <h1 class="text-3xl font-bold text-gray-800">Add New Product</h1>
    </div>

    <div class="bg-white p-8 rounded-lg shadow-lg">
        <form @submit.prevent="submitForm">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <!-- Left Column -->
                <div>
                    <div class="mb-4">
                        <label for="name" class="block text-sm font-medium text-gray-700">Product Name</label>
                        <input type="text" id="name" x-model="form.name" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm" required>
                    </div>
                    <div class="mb-4">
                        <label for="description" class="block text-sm font-medium text-gray-700">Description</label>
                        <textarea id="description" x-model="form.description" rows="4" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm"></textarea>
                    </div>
                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        <div class="mb-4">
                            <label for="price" class="block text-sm font-medium text-gray-700">Price</label>
                            <input type="number" id="price" x-model="form.price" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm" required>
                        </div>
                        <div class="mb-4">
                            <label for="stock" class="block text-sm font-medium text-gray-700">Stock</label>
                            <input type="number" id="stock" x-model="form.stock" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm" required>
                        </div>
                    </div>
                    <div class="mb-4">
                        <label for="category" class="block text-sm font-medium text-gray-700">Category</label>
                        <select id="category" x-model="form.category" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm">
                            <option value="">Select a category</option>
                            <template x-for="cat in categories" :key="cat">
                                <option :value="cat" x-text="cat"></option>
                            </template>
                        </select>
                    </div>
                </div>

                <!-- Right Column -->
                <div>
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700">Product Image</label>
                        <div class="mt-1 flex items-center">
                            <template x-if="imagePreview">
                                <div class="relative">
                                    <img :src="imagePreview" class="h-24 w-24 rounded-md object-cover">
                                    <button @click="removeImage" type="button" class="absolute top-0 right-0 -mt-2 -mr-2 bg-red-500 text-white rounded-full p-1">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </template>
                            <template x-if="!imagePreview">
                                <div class="h-24 w-24 bg-gray-100 rounded-md flex items-center justify-center">
                                    <i class="fas fa-image text-4xl text-gray-400"></i>
                                </div>
                            </template>
                            <div class="ml-4">
                                <input type="file" id="image" @change="handleImageUpload" class="hidden">
                                <label for="image" class="cursor-pointer bg-white py-2 px-3 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">
                                    Upload Image
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="mb-4">
                        <label for="sku" class="block text-sm font-medium text-gray-700">SKU</label>
                        <input type="text" id="sku" x-model="form.sku" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm">
                    </div>
                </div>
            </div>

            <div class="mt-8 flex justify-end">
                <button type="submit" class="bg-green-600 text-white px-6 py-2 rounded-md hover:bg-green-700">Create Product</button>
            </div>
        </form>
    </div>
</div>

<script>
function addProductForm() {
    return {
        form: {
            name: '',
            description: '',
            price: '',
            stock: '',
            category: '',
            sku: ''
        },
        imagePreview: null,
        categories: ['Electronics', 'Clothing', 'Books', 'Home Goods', 'Sports'],

        handleImageUpload(event) {
            const file = event.target.files[0];
            if (file) {
                this.imagePreview = URL.createObjectURL(file);
            }
        },

        removeImage() {
            this.imagePreview = null;
            document.getElementById('image').value = '';
        },

        submitForm() {
            // In a real application, you would send this data to the server.
            console.log('Form submitted:', this.form);
            alert('Product created successfully!');
            
            // Reset form
            this.form = { name: '', description: '', price: '', stock: '', category: '', sku: '' };
            this.removeImage();
        }
    }
}
</script>
@endsection

