@extends('layouts.vendor')

@section('title', 'Products')

@push('page-styles')
<link rel="stylesheet" href="{{ asset('css/vendor/pages/products.css') }}">
@endpush

@section('content')
<div x-data="vendorProducts()">
    <!-- Products Header -->
    <div class="products-header">
        <div class="products-header-left">
            <h1 class="products-title">Products Management</h1>
            <p class="products-subtitle">Manage your product catalog, inventory, and pricing</p>
        </div>
        <div class="products-header-right">
            <button class="btn btn-secondary">
                <i class="fas fa-download"></i>
                <span>Export</span>
            </button>
            <a href="{{ route('vendor.products.create') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i>
                <span>Add Product</span>
            </a>
        </div>
    </div>

    <!-- Filters & Search -->
    <div class="products-filters">
        <div class="filters-row">
            <div class="filter-group">
                <label class="filter-label">Search Products</label>
                <div class="search-input-container">
                    <i class="search-icon fas fa-search"></i>
                    <input type="text" x-model="searchTerm" placeholder="Search by name, SKU, or description..." class="filter-input search-input">
                </div>
            </div>

            <div class="filter-group">
                <label class="filter-label">Category</label>
                <select x-model="categoryFilter" class="filter-select">
                    <option value="">All Categories</option>
                    <option value="electronics">Electronics</option>
                    <option value="clothing">Clothing</option>
                    <option value="home">Home & Garden</option>
                    <option value="books">Books</option>
                    <option value="sports">Sports</option>
                </select>
            </div>

            <div class="filter-group">
                <label class="filter-label">Status</label>
                <select x-model="statusFilter" class="filter-select">
                    <option value="">All Status</option>
                    <option value="active">Active</option>
                    <option value="inactive">Inactive</option>
                    <option value="draft">Draft</option>
                </select>
            </div>

            <div class="filter-group">
                <label class="filter-label">Stock</label>
                <select x-model="stockFilter" class="filter-select">
                    <option value="">All Stock</option>
                    <option value="in-stock">In Stock</option>
                    <option value="low-stock">Low Stock</option>
                    <option value="out-of-stock">Out of Stock</option>
                </select>
            </div>

            <div class="filter-actions">
                <button @click="clearFilters()" class="btn btn-ghost">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- Products Grid -->
    <div class="products-grid" x-show="filteredProducts.length > 0">
        <template x-for="product in filteredProducts" :key="product.id">
            <div class="product-card">
                <!-- Product Image -->
                <div class="product-image-container">
                    <img x-show="product.image" :src="product.image" :alt="product.name" class="product-image">
                    <div x-show="!product.image" class="product-image-placeholder">
                        <i class="fas fa-image"></i>
                    </div>

                    <!-- Status Badge -->
                    <div class="product-status-badge" :class="product.status">
                        <span x-text="product.status"></span>
                    </div>

                    <!-- Actions Overlay -->
                    <div class="product-actions-overlay">
                        <button class="product-action-btn" title="Quick Edit">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="product-action-btn" title="Duplicate">
                            <i class="fas fa-copy"></i>
                        </button>
                        <button class="product-action-btn" title="Delete">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>

                <!-- Product Content -->
                <div class="product-content">
                    <div class="product-category" x-text="product.category"></div>
                    <h3 class="product-name" x-text="product.name"></h3>
                    <p class="product-description" x-text="product.description"></p>

                    <div class="product-meta">
                        <div class="product-price" x-text="'₹' + product.price.toLocaleString()"></div>
                        <div class="product-stock" :class="{ 'low': product.stock < 10, 'out': product.stock === 0 }">
                            <span x-text="product.stock + ' in stock'"></span>
                        </div>
                    </div>

                    <div class="product-footer">
                        <a :href="'/vendor/products/' + product.id + '/edit'" class="product-btn">
                            <i class="fas fa-edit"></i>
                            <span>Edit</span>
                        </a>
                        <button @click="viewProduct(product)" class="product-btn primary">
                            <i class="fas fa-eye"></i>
                            <span>View</span>
                        </button>
                    </div>
                </div>
            </div>
        </template>
    </div>

    <!-- Empty State -->
    <div x-show="filteredProducts.length === 0" class="products-empty">
        <div class="empty-icon">
            <i class="fas fa-box-open"></i>
        </div>
        <h3 class="empty-title">No products found</h3>
        <p class="empty-description">
            <span x-show="searchTerm || categoryFilter || statusFilter || stockFilter">
                Try adjusting your filters or search terms to find what you're looking for.
            </span>
            <span x-show="!searchTerm && !categoryFilter && !statusFilter && !stockFilter">
                Get started by adding your first product to your store.
            </span>
        </p>
        <a href="{{ route('vendor.products.create') }}" class="btn btn-primary">
            <i class="fas fa-plus"></i>
            <span>Add Your First Product</span>
        </a>
    </div>

    <!-- Pagination -->
    <div x-show="filteredProducts.length > 0" class="products-pagination">
        <button class="pagination-btn" :class="{ 'disabled': currentPage === 1 }" @click="previousPage()">
            <i class="fas fa-chevron-left"></i>
        </button>

        <template x-for="page in visiblePages" :key="page">
            <button class="pagination-btn" :class="{ 'active': page === currentPage }" @click="goToPage(page)" x-text="page"></button>
        </template>

        <button class="pagination-btn" :class="{ 'disabled': currentPage === totalPages }" @click="nextPage()">
            <i class="fas fa-chevron-right"></i>
        </button>

        <div class="pagination-info">
            <span x-text="'Showing ' + ((currentPage - 1) * itemsPerPage + 1) + '-' + Math.min(currentPage * itemsPerPage, filteredProducts.length) + ' of ' + filteredProducts.length + ' products'"></span>
        </div>
    </div>
</div>

@push('scripts')
<script>
function vendorProducts() {
    return {
        products: [],
        searchTerm: '',
        categoryFilter: '',
        statusFilter: '',
        stockFilter: '',
        currentPage: 1,
        itemsPerPage: 12,

        init() {
            this.products = [
                {
                    id: 1,
                    name: 'Premium Wireless Headphones',
                    description: 'High-quality wireless headphones with noise cancellation',
                    price: 4500,
                    stock: 25,
                    category: 'electronics',
                    status: 'active',
                    image: 'https://via.placeholder.com/300x200/7ED957/ffffff?text=Headphones'
                },
                {
                    id: 2,
                    name: 'Smart Fitness Watch',
                    description: 'Track your fitness goals with this advanced smartwatch',
                    price: 8900,
                    stock: 15,
                    category: 'electronics',
                    status: 'active',
                    image: 'https://via.placeholder.com/300x200/7ED957/ffffff?text=Watch'
                },
                {
                    id: 3,
                    name: 'Organic Cotton T-Shirt',
                    description: 'Comfortable and sustainable cotton t-shirt',
                    price: 1200,
                    stock: 50,
                    category: 'clothing',
                    status: 'active',
                    image: 'https://via.placeholder.com/300x200/7ED957/ffffff?text=T-Shirt'
                },
                {
                    id: 4,
                    name: 'Gaming Laptop Pro',
                    description: 'High-performance laptop for gaming and productivity',
                    price: 85000,
                    stock: 8,
                    category: 'electronics',
                    status: 'active',
                    image: 'https://via.placeholder.com/300x200/7ED957/ffffff?text=Laptop'
                },
                {
                    id: 5,
                    name: 'Yoga Mat Premium',
                    description: 'Non-slip yoga mat for your daily practice',
                    price: 2500,
                    stock: 0,
                    category: 'sports',
                    status: 'inactive',
                    image: 'https://via.placeholder.com/300x200/7ED957/ffffff?text=Yoga+Mat'
                },
                {
                    id: 6,
                    name: 'Coffee Maker Deluxe',
                    description: 'Automatic coffee maker with programmable settings',
                    price: 6500,
                    stock: 12,
                    category: 'home',
                    status: 'draft',
                    image: 'https://via.placeholder.com/300x200/7ED957/ffffff?text=Coffee+Maker'
                }
            ];
        },

        get filteredProducts() {
            return this.products.filter(product => {
                const searchMatch = !this.searchTerm ||
                    product.name.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
                    product.description.toLowerCase().includes(this.searchTerm.toLowerCase());

                const categoryMatch = !this.categoryFilter || product.category === this.categoryFilter;
                const statusMatch = !this.statusFilter || product.status === this.statusFilter;

                let stockMatch = true;
                if (this.stockFilter === 'in-stock') stockMatch = product.stock > 10;
                else if (this.stockFilter === 'low-stock') stockMatch = product.stock > 0 && product.stock <= 10;
                else if (this.stockFilter === 'out-of-stock') stockMatch = product.stock === 0;

                return searchMatch && categoryMatch && statusMatch && stockMatch;
            });
        },

        get totalPages() {
            return Math.ceil(this.filteredProducts.length / this.itemsPerPage);
        },

        get visiblePages() {
            const pages = [];
            const start = Math.max(1, this.currentPage - 2);
            const end = Math.min(this.totalPages, this.currentPage + 2);

            for (let i = start; i <= end; i++) {
                pages.push(i);
            }
            return pages;
        },

        clearFilters() {
            this.searchTerm = '';
            this.categoryFilter = '';
            this.statusFilter = '';
            this.stockFilter = '';
            this.currentPage = 1;
        },

        goToPage(page) {
            this.currentPage = page;
        },

        previousPage() {
            if (this.currentPage > 1) {
                this.currentPage--;
            }
        },

        nextPage() {
            if (this.currentPage < this.totalPages) {
                this.currentPage++;
            }
        },

        viewProduct(product) {
            // Navigate to product view page
            window.location.href = `/vendor/products/${product.id}`;
        },

        deleteProduct(id) {
            if (confirm('Are you sure you want to delete this product?')) {
                this.products = this.products.filter(p => p.id !== id);
                window.showToast('Product deleted successfully', 'success');
            }
        }
    }
}
</script>
@endpush
@endsection

