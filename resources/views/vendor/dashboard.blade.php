@extends('layouts.vendor')

@section('title', 'Dashboard')
@section('page-title', 'Dashboard')

@push('page-styles')
<link rel="stylesheet" href="{{ asset('css/vendor/pages/modern-dashboard.css') }}">
@endpush

@section('content')
<!-- Modern Welcome Section -->
<div class="modern-welcome-section">
    <div class="welcome-container">
        <div class="welcome-content">
            <div class="welcome-header">
                <div class="welcome-badge">
                    <i class="fas fa-crown"></i>
                    <span>Premium Vendor</span>
                </div>
                <h1 class="welcome-title">
                    Good {{ date('H') < 12 ? 'morning' : (date('H') < 18 ? 'afternoon' : 'evening') }},
                    <span class="highlight">{{ Auth::user()->name }}</span>! 👋
                </h1>
                <p class="welcome-description">
                    Here's what's happening with your store today. Track your performance and grow your business with WhaMart's powerful tools.
                </p>
            </div>

            <div class="welcome-actions">
                <a href="{{ route('vendor.products.create') }}" class="btn btn-primary">
                    <i class="fas fa-plus"></i>
                    <span>Add Product</span>
                </a>
                <a href="{{ route('vendor.orders') }}" class="btn btn-secondary">
                    <i class="fas fa-shopping-bag"></i>
                    <span>View Orders</span>
                </a>
                <a href="{{ route('vendor.analytics') }}" class="btn btn-outline">
                    <i class="fas fa-chart-line"></i>
                    <span>Analytics</span>
                </a>
            </div>
        </div>

        <div class="welcome-visual">
            <div class="visual-container">
                <div class="floating-elements">
                    <div class="floating-card card-1">
                        <i class="fas fa-rupee-sign"></i>
                        <span>₹45.2K</span>
                    </div>
                    <div class="floating-card card-2">
                        <i class="fas fa-shopping-cart"></i>
                        <span>23 Orders</span>
                    </div>
                    <div class="floating-card card-3">
                        <i class="fas fa-users"></i>
                        <span>156 Customers</span>
                    </div>
                </div>
                <div class="main-visual">
                    <div class="store-icon">
                        <i class="fas fa-store"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modern Stats Grid -->
<div class="modern-stats-section">
    <div class="stats-header">
        <h2 class="stats-title">Performance Overview</h2>
        <div class="stats-controls">
            <div class="time-selector" x-data="{ selected: 'today' }">
                <button @click="selected = 'today'" :class="{ 'active': selected === 'today' }" class="time-btn">Today</button>
                <button @click="selected = 'week'" :class="{ 'active': selected === 'week' }" class="time-btn">This Week</button>
                <button @click="selected = 'month'" :class="{ 'active': selected === 'month' }" class="time-btn">This Month</button>
            </div>
        </div>
    </div>

    <div class="stats-grid">
        <!-- Revenue Card -->
        <div class="stat-card success" x-data="{ menuOpen: false }">
            <div class="stat-header">
                <div class="stat-icon">
                    <i class="fas fa-rupee-sign"></i>
                </div>
                <div class="stat-menu-container">
                    <button @click="menuOpen = !menuOpen" class="stat-menu">
                        <i class="fas fa-ellipsis-h"></i>
                    </button>
                    <div x-show="menuOpen" @click.away="menuOpen = false" class="stat-dropdown">
                        <a href="#" class="stat-dropdown-item">
                            <i class="fas fa-eye"></i>
                            <span>View Details</span>
                        </a>
                        <a href="#" class="stat-dropdown-item">
                            <i class="fas fa-download"></i>
                            <span>Export Data</span>
                        </a>
                    </div>
                </div>
            </div>
            <div class="stat-content">
                <div class="stat-label">Today's Revenue</div>
                <div class="stat-value">₹45,280</div>
                <div class="stat-change positive">
                    <i class="fas fa-arrow-up"></i>
                    <span>12.5% from yesterday</span>
                </div>
            </div>
            <div class="stat-footer">
                <span class="stat-period">Last 24 hours</span>
                <a href="{{ route('vendor.analytics') }}" class="stat-link">View details</a>
            </div>
        </div>

        <!-- Orders Card -->
        <div class="stat-card" x-data="{ menuOpen: false }">
            <div class="stat-header">
                <div class="stat-icon">
                    <i class="fas fa-shopping-bag"></i>
                </div>
                <div class="stat-menu-container">
                    <button @click="menuOpen = !menuOpen" class="stat-menu">
                        <i class="fas fa-ellipsis-h"></i>
                    </button>
                    <div x-show="menuOpen" @click.away="menuOpen = false" class="stat-dropdown">
                        <a href="{{ route('vendor.orders') }}" class="stat-dropdown-item">
                            <i class="fas fa-list"></i>
                            <span>View All Orders</span>
                        </a>
                        <a href="#" class="stat-dropdown-item">
                            <i class="fas fa-filter"></i>
                            <span>Filter Orders</span>
                        </a>
                    </div>
                </div>
            </div>
            <div class="stat-content">
                <div class="stat-label">New Orders</div>
                <div class="stat-value">23</div>
                <div class="stat-change positive">
                    <i class="fas fa-arrow-up"></i>
                    <span>8.2% from yesterday</span>
                </div>
            </div>
            <div class="stat-footer">
                <span class="stat-period">Last 24 hours</span>
                <a href="{{ route('vendor.orders') }}" class="stat-link">View orders</a>
            </div>
        </div>

        <!-- Customers Card -->
        <div class="stat-card success" x-data="{ menuOpen: false }">
            <div class="stat-header">
                <div class="stat-icon">
                    <i class="fas fa-users"></i>
                </div>
                <div class="stat-menu-container">
                    <button @click="menuOpen = !menuOpen" class="stat-menu">
                        <i class="fas fa-ellipsis-h"></i>
                    </button>
                    <div x-show="menuOpen" @click.away="menuOpen = false" class="stat-dropdown">
                        <a href="{{ route('vendor.customers') }}" class="stat-dropdown-item">
                            <i class="fas fa-users"></i>
                            <span>View Customers</span>
                        </a>
                        <a href="#" class="stat-dropdown-item">
                            <i class="fas fa-user-plus"></i>
                            <span>Add Customer</span>
                        </a>
                    </div>
                </div>
            </div>
            <div class="stat-content">
                <div class="stat-label">Active Customers</div>
                <div class="stat-value">156</div>
                <div class="stat-change positive">
                    <i class="fas fa-arrow-up"></i>
                    <span>5.3% from last week</span>
                </div>
            </div>
            <div class="stat-footer">
                <span class="stat-period">This month</span>
                <a href="{{ route('vendor.customers') }}" class="stat-link">View customers</a>
            </div>
        </div>

        <!-- Products Card -->
        <div class="stat-card warning" x-data="{ menuOpen: false }">
            <div class="stat-header">
                <div class="stat-icon">
                    <i class="fas fa-box"></i>
                </div>
                <div class="stat-menu-container">
                    <button @click="menuOpen = !menuOpen" class="stat-menu">
                        <i class="fas fa-ellipsis-h"></i>
                    </button>
                    <div x-show="menuOpen" @click.away="menuOpen = false" class="stat-dropdown">
                        <a href="{{ route('vendor.products') }}" class="stat-dropdown-item">
                            <i class="fas fa-list"></i>
                            <span>View Products</span>
                        </a>
                        <a href="{{ route('vendor.products.create') }}" class="stat-dropdown-item">
                            <i class="fas fa-plus"></i>
                            <span>Add Product</span>
                        </a>
                    </div>
                </div>
            </div>
            <div class="stat-content">
                <div class="stat-label">Total Products</div>
                <div class="stat-value">120</div>
                <div class="stat-change negative">
                    <i class="fas fa-arrow-down"></i>
                    <span>2.1% from last month</span>
                </div>
            </div>
            <div class="stat-footer">
                <span class="stat-period">All time</span>
                <a href="{{ route('vendor.products') }}" class="stat-link">Manage products</a>
            </div>
        </div>
    </div>
</div>

<!-- Modern Charts & Analytics Section -->
<div class="modern-charts-section">
    <div class="charts-grid">
        <!-- Revenue Chart -->
        <div class="chart-card main-chart">
            <div class="chart-header">
                <div class="chart-title-section">
                    <h3 class="chart-title">Revenue Analytics</h3>
                    <p class="chart-subtitle">Track your earnings over time</p>
                </div>
                <div class="chart-controls" x-data="{ period: '7d' }">
                    <button @click="period = '7d'" :class="{ 'active': period === '7d' }" class="chart-control">7D</button>
                    <button @click="period = '30d'" :class="{ 'active': period === '30d' }" class="chart-control">30D</button>
                    <button @click="period = '90d'" :class="{ 'active': period === '90d' }" class="chart-control">90D</button>
                    <button @click="period = '1y'" :class="{ 'active': period === '1y' }" class="chart-control">1Y</button>
                </div>
            </div>
            <div class="chart-body">
                <div class="chart-container">
                    <canvas id="revenueChart" width="400" height="200"></canvas>
                </div>
                <div class="chart-insights">
                    <div class="insight-item">
                        <div class="insight-label">Peak Day</div>
                        <div class="insight-value">₹8,450</div>
                    </div>
                    <div class="insight-item">
                        <div class="insight-label">Average</div>
                        <div class="insight-value">₹6,240</div>
                    </div>
                    <div class="insight-item">
                        <div class="insight-label">Growth</div>
                        <div class="insight-value text-success">+12.5%</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="chart-card activity-card">
            <div class="chart-header">
                <div class="chart-title-section">
                    <h3 class="chart-title">Recent Activity</h3>
                    <p class="chart-subtitle">Latest updates from your store</p>
                </div>
                <a href="#" class="view-all-link">
                    <span>View all</span>
                    <i class="fas fa-arrow-right"></i>
                </a>
            </div>
            <div class="chart-body">
                <div class="activity-list">
                    <div class="activity-item">
                        <div class="activity-icon success">
                            <i class="fas fa-shopping-bag"></i>
                        </div>
                        <div class="activity-content">
                            <div class="activity-title">New order received</div>
                            <div class="activity-description">Order #ORD-1289 from Rahul Sharma</div>
                            <div class="activity-meta">
                                <span class="activity-amount">₹2,450</span>
                                <span class="activity-time">2 min ago</span>
                            </div>
                        </div>
                        <div class="activity-status">
                            <span class="badge badge-success">New</span>
                        </div>
                    </div>

                    <div class="activity-item">
                        <div class="activity-icon warning">
                            <i class="fas fa-star"></i>
                        </div>
                        <div class="activity-content">
                            <div class="activity-title">New review received</div>
                            <div class="activity-description">5-star review on "Premium Headphones"</div>
                            <div class="activity-meta">
                                <span class="activity-rating">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                </span>
                                <span class="activity-time">15 min ago</span>
                            </div>
                        </div>
                        <div class="activity-status">
                            <span class="badge badge-warning">Review</span>
                        </div>
                    </div>

                    <div class="activity-item">
                        <div class="activity-icon info">
                            <i class="fas fa-box"></i>
                        </div>
                        <div class="activity-content">
                            <div class="activity-title">Product updated</div>
                            <div class="activity-description">Stock updated for "Wireless Mouse"</div>
                            <div class="activity-meta">
                                <span class="activity-stock">Stock: 45 units</span>
                                <span class="activity-time">1 hour ago</span>
                            </div>
                        </div>
                        <div class="activity-status">
                            <span class="badge badge-info">Update</span>
                        </div>
                    </div>

                    <div class="activity-item">
                        <div class="activity-icon primary">
                            <i class="fas fa-user-plus"></i>
                        </div>
                        <div class="activity-content">
                            <div class="activity-title">New customer registered</div>
                            <div class="activity-description">Priya Singh joined your store</div>
                            <div class="activity-meta">
                                <span class="activity-location">Mumbai, India</span>
                                <span class="activity-time">3 hours ago</span>
                            </div>
                        </div>
                        <div class="activity-status">
                            <span class="badge badge-primary">Customer</span>
                        </div>
                    </div>

                    <div class="activity-item">
                        <div class="activity-icon error">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="activity-content">
                            <div class="activity-title">Low stock alert</div>
                            <div class="activity-description">"Gaming Keyboard" is running low</div>
                            <div class="activity-meta">
                                <span class="activity-stock">Only 3 left</span>
                                <span class="activity-time">5 hours ago</span>
                            </div>
                        </div>
                        <div class="activity-status">
                            <span class="badge badge-error">Alert</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modern Quick Actions Section -->
<div class="modern-quick-actions-section">
    <div class="section-header">
        <h2 class="section-title">Quick Actions</h2>
        <p class="section-subtitle">Manage your store efficiently with these shortcuts</p>
    </div>

    <div class="quick-actions-grid">
        <a href="{{ route('vendor.products.create') }}" class="quick-action-card primary">
            <div class="quick-action-icon">
                <i class="fas fa-plus"></i>
            </div>
            <div class="quick-action-content">
                <h4 class="quick-action-title">Add Product</h4>
                <p class="quick-action-description">Create a new product listing and start selling</p>
            </div>
            <div class="quick-action-arrow">
                <i class="fas fa-arrow-right"></i>
            </div>
        </a>

        <a href="{{ route('vendor.orders') }}" class="quick-action-card success">
            <div class="quick-action-icon">
                <i class="fas fa-shopping-bag"></i>
            </div>
            <div class="quick-action-content">
                <h4 class="quick-action-title">Manage Orders</h4>
                <p class="quick-action-description">Process and track your customer orders</p>
            </div>
            <div class="quick-action-arrow">
                <i class="fas fa-arrow-right"></i>
            </div>
        </a>

        <a href="{{ route('vendor.analytics') }}" class="quick-action-card info">
            <div class="quick-action-icon">
                <i class="fas fa-chart-line"></i>
            </div>
            <div class="quick-action-content">
                <h4 class="quick-action-title">View Analytics</h4>
                <p class="quick-action-description">Track performance and growth metrics</p>
            </div>
            <div class="quick-action-arrow">
                <i class="fas fa-arrow-right"></i>
            </div>
        </a>

        <a href="{{ route('vendor.customers') }}" class="quick-action-card warning">
            <div class="quick-action-icon">
                <i class="fas fa-users"></i>
            </div>
            <div class="quick-action-content">
                <h4 class="quick-action-title">Customer Insights</h4>
                <p class="quick-action-description">Understand your customer behavior</p>
            </div>
            <div class="quick-action-arrow">
                <i class="fas fa-arrow-right"></i>
            </div>
        </a>

        <a href="{{ route('vendor.inventory') }}" class="quick-action-card secondary">
            <div class="quick-action-icon">
                <i class="fas fa-warehouse"></i>
            </div>
            <div class="quick-action-content">
                <h4 class="quick-action-title">Inventory</h4>
                <p class="quick-action-description">Manage stock levels and inventory</p>
            </div>
            <div class="quick-action-arrow">
                <i class="fas fa-arrow-right"></i>
            </div>
        </a>

        <a href="{{ route('vendor.marketing') }}" class="quick-action-card error">
            <div class="quick-action-icon">
                <i class="fas fa-bullhorn"></i>
            </div>
            <div class="quick-action-content">
                <h4 class="quick-action-title">Marketing</h4>
                <p class="quick-action-description">Promote your products and boost sales</p>
            </div>
            <div class="quick-action-arrow">
                <i class="fas fa-arrow-right"></i>
            </div>
        </a>
    </div>
</div>

<!-- Modern Recent Orders Section -->
<div class="modern-orders-section">
    <div class="section-header">
        <div class="section-title-group">
            <h2 class="section-title">Recent Orders</h2>
            <p class="section-subtitle">Latest orders from your customers</p>
        </div>
        <div class="section-actions">
            <a href="{{ route('vendor.orders') }}" class="view-all-link">
                <span>View All Orders</span>
                <i class="fas fa-arrow-right"></i>
            </a>
        </div>
    </div>

    <div class="orders-container">
        <div class="orders-list">
            <div class="order-item">
                <div class="order-info">
                    <div class="order-header">
                        <div class="order-id">#ORD-1289</div>
                        <div class="order-time">2 min ago</div>
                    </div>
                    <div class="order-customer">
                        <div class="customer-avatar">
                            <img src="https://ui-avatars.com/api/?name=Rahul+Sharma&background=7ED957&color=fff" alt="Rahul Sharma">
                        </div>
                        <div class="customer-details">
                            <div class="customer-name">Rahul Sharma</div>
                            <div class="customer-location">Mumbai, Maharashtra</div>
                        </div>
                    </div>
                </div>
                <div class="order-details">
                    <div class="order-amount">₹2,450</div>
                    <div class="order-items">3 items</div>
                </div>
                <div class="order-status">
                    <span class="badge badge-success">
                        <i class="fas fa-check-circle"></i>
                        <span>Confirmed</span>
                    </span>
                </div>
                <div class="order-actions" x-data="{ menuOpen: false }">
                    <button @click="menuOpen = !menuOpen" class="order-menu-btn">
                        <i class="fas fa-ellipsis-h"></i>
                    </button>
                    <div x-show="menuOpen" @click.away="menuOpen = false" class="order-dropdown">
                        <a href="#" class="order-dropdown-item">
                            <i class="fas fa-eye"></i>
                            <span>View Details</span>
                        </a>
                        <a href="#" class="order-dropdown-item">
                            <i class="fas fa-edit"></i>
                            <span>Update Status</span>
                        </a>
                        <a href="#" class="order-dropdown-item">
                            <i class="fas fa-print"></i>
                            <span>Print Invoice</span>
                        </a>
                    </div>
                </div>
            </div>

            <div class="order-item">
                <div class="order-info">
                    <div class="order-header">
                        <div class="order-id">#ORD-1288</div>
                        <div class="order-time">15 min ago</div>
                    </div>
                    <div class="order-customer">
                        <div class="customer-avatar">
                            <img src="https://ui-avatars.com/api/?name=Priya+Patel&background=F59E0B&color=fff" alt="Priya Patel">
                        </div>
                        <div class="customer-details">
                            <div class="customer-name">Priya Patel</div>
                            <div class="customer-location">Delhi, India</div>
                        </div>
                    </div>
                </div>
                <div class="order-details">
                    <div class="order-amount">₹3,299</div>
                    <div class="order-items">1 item</div>
                </div>
                <div class="order-status">
                    <span class="badge badge-warning">
                        <i class="fas fa-clock"></i>
                        <span>Processing</span>
                    </span>
                </div>
                <div class="order-actions" x-data="{ menuOpen: false }">
                    <button @click="menuOpen = !menuOpen" class="order-menu-btn">
                        <i class="fas fa-ellipsis-h"></i>
                    </button>
                    <div x-show="menuOpen" @click.away="menuOpen = false" class="order-dropdown">
                        <a href="#" class="order-dropdown-item">
                            <i class="fas fa-eye"></i>
                            <span>View Details</span>
                        </a>
                        <a href="#" class="order-dropdown-item">
                            <i class="fas fa-check"></i>
                            <span>Mark as Shipped</span>
                        </a>
                    </div>
                </div>
            </div>

            <div class="order-item">
                <div class="order-info">
                    <div class="order-header">
                        <div class="order-id">#ORD-1287</div>
                        <div class="order-time">1 hour ago</div>
                    </div>
                    <div class="order-customer">
                        <div class="customer-avatar">
                            <img src="https://ui-avatars.com/api/?name=Vikram+Singh&background=3B82F6&color=fff" alt="Vikram Singh">
                        </div>
                        <div class="customer-details">
                            <div class="customer-name">Vikram Singh</div>
                            <div class="customer-location">Bangalore, Karnataka</div>
                        </div>
                    </div>
                </div>
                <div class="order-details">
                    <div class="order-amount">₹5,499</div>
                    <div class="order-items">2 items</div>
                </div>
                <div class="order-status">
                    <span class="badge badge-info">
                        <i class="fas fa-truck"></i>
                        <span>Shipped</span>
                    </span>
                </div>
                <div class="order-actions" x-data="{ menuOpen: false }">
                    <button @click="menuOpen = !menuOpen" class="order-menu-btn">
                        <i class="fas fa-ellipsis-h"></i>
                    </button>
                    <div x-show="menuOpen" @click.away="menuOpen = false" class="order-dropdown">
                        <a href="#" class="order-dropdown-item">
                            <i class="fas fa-eye"></i>
                            <span>View Details</span>
                        </a>
                        <a href="#" class="order-dropdown-item">
                            <i class="fas fa-map-marker-alt"></i>
                            <span>Track Package</span>
                        </a>
                    </div>
                </div>
            </div>

            <div class="order-item">
                <div class="order-info">
                    <div class="order-header">
                        <div class="order-id">#ORD-1286</div>
                        <div class="order-time">3 hours ago</div>
                    </div>
                    <div class="order-customer">
                        <div class="customer-avatar">
                            <img src="https://ui-avatars.com/api/?name=Ananya+Gupta&background=EF4444&color=fff" alt="Ananya Gupta">
                        </div>
                        <div class="customer-details">
                            <div class="customer-name">Ananya Gupta</div>
                            <div class="customer-location">Chennai, Tamil Nadu</div>
                        </div>
                    </div>
                </div>
                <div class="order-details">
                    <div class="order-amount">₹2,199</div>
                    <div class="order-items">1 item</div>
                </div>
                <div class="order-status">
                    <span class="badge badge-error">
                        <i class="fas fa-times-circle"></i>
                        <span>Cancelled</span>
                    </span>
                </div>
                <div class="order-actions" x-data="{ menuOpen: false }">
                    <button @click="menuOpen = !menuOpen" class="order-menu-btn">
                        <i class="fas fa-ellipsis-h"></i>
                    </button>
                    <div x-show="menuOpen" @click.away="menuOpen = false" class="order-dropdown">
                        <a href="#" class="order-dropdown-item">
                            <i class="fas fa-eye"></i>
                            <span>View Details</span>
                        </a>
                        <a href="#" class="order-dropdown-item">
                            <i class="fas fa-undo"></i>
                            <span>Refund</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
    <!-- Recent Activity -->
    <div class="card">
        <div class="card-header">
            <h2 class="card-title">Recent Activity</h2>
        </div>
        <div class="space-y-4 p-4">
            @foreach([
                ['icon' => 'shopping-cart', 'color' => 'text-blue-500', 'text' => 'New order #1290 received', 'time' => '2 min ago'],
                ['icon' => 'user', 'color' => 'text-green-500', 'text' => 'New customer registered', 'time' => '1 hour ago'],
                ['icon' => 'star', 'color' => 'text-yellow-500', 'text' => 'Product review received', 'time' => '3 hours ago'],
                ['icon' => 'money-bill-wave', 'color' => 'text-purple-500', 'text' => 'Payment of ₹8,750 received', 'time' => '5 hours ago'],
                ['icon' => 'truck', 'color' => 'text-indigo-500', 'text' => 'Order #1285 has been shipped', 'time' => '1 day ago'],
            ] as $activity)
            <div class="flex items-start">
                <div class="flex-shrink-0 h-10 w-10 rounded-full bg-gray-100 dark:bg-gray-700 flex items-center justify-center mr-3">
                    <i class="fas fa-{{ $activity['icon'] }} {{ $activity['color'] }}"></i>
                </div>
                <div class="flex-1">
                    <p class="text-sm font-medium text-gray-900 dark:text-white">{{ $activity['text'] }}</p>
                    <p class="text-xs text-gray-500 dark:text-gray-400">{{ $activity['time'] }}</p>
                </div>
            </div>
            @endforeach
        </div>
    </div>

    <!-- Top Products -->
    <div class="card">
        <div class="card-header">
            <h2 class="card-title">Top Selling Products</h2>
        </div>
        <div class="space-y-4 p-4">
            @foreach([
                ['name' => 'Wireless Earbuds', 'sales' => '1,245', 'revenue' => '₹1,24,500', 'change' => '12.5%', 'trend' => 'up'],
                ['name' => 'Smart Watch', 'sales' => '987', 'revenue' => '₹1,97,400', 'change' => '8.3%', 'trend' => 'up'],
                ['name' => 'Bluetooth Speaker', 'sales' => '756', 'revenue' => '₹1,13,400', 'change' => '5.7%', 'trend' => 'up'],
                ['name' => 'Power Bank', 'sales' => '543', 'revenue' => '₹81,450', 'change' => '2.1%', 'trend' => 'down'],
                ['name' => 'Phone Case', 'sales' => '321', 'revenue' => '₹16,050', 'change' => '1.2%', 'trend' => 'up'],
            ] as $product)
            <div class="flex items-center justify-between">
                <div class="flex-1 min-w-0">
                    <p class="text-sm font-medium text-gray-900 dark:text-white truncate">{{ $product['name'] }}</p>
                    <div class="flex items-center text-xs text-gray-500 dark:text-gray-400">
                        <span>{{ $product['sales'] }} sales</span>
                        <span class="mx-2">•</span>
                        <span>{{ $product['revenue'] }}</span>
                    </div>
                </div>
                <div class="ml-4 flex-shrink-0">
                    <span class="inline-flex items-center text-xs font-medium {{ $product['trend'] === 'up' ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400' }}">
                        @if($product['trend'] === 'up')
                            <i class="fas fa-arrow-up mr-1"></i>
                        @else
                            <i class="fas fa-arrow-down mr-1"></i>
                        @endif
                        {{ $product['change'] }}
                    </span>
                </div>
            </div>
            @endforeach
        </div>
    </div>
</div>

<!-- Performance Overview -->
<div class="card mb-8">
    <div class="card-header">
        <h2 class="card-title">Performance Overview</h2>
        <div class="flex items-center space-x-2">
            <button class="text-sm px-3 py-1 rounded-md bg-primary-100 dark:bg-primary-900 text-primary-700 dark:text-primary-200">
                This Month
            </button>
            <button class="text-sm px-3 py-1 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-300">
                Last Month
            </button>
            <button class="text-sm px-3 py-1 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-300">
                This Year
            </button>
        </div>
    </div>
    <div class="p-6">
        <div class="h-80">
            <!-- Chart will be rendered by ApexCharts -->
            <div id="performanceChart"></div>
        </div>
    </div>
</div>

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize ApexCharts
        var options = {
            series: [{
                name: 'Revenue',
                type: 'column',
                data: [44000, 55000, 41000, 67000, 58000, 72000, 69000, 91000, 102000, 83000, 93000, 108000]
            }, {
                name: 'Orders',
                type: 'line',
                data: [300, 350, 280, 420, 380, 450, 430, 520, 590, 480, 540, 600]
            }],
            chart: {
                height: '100%',
                type: 'line',
                stacked: false,
                toolbar: {
                    show: true
                },
                zoom: {
                    enabled: true
                },
                fontFamily: 'Inter, sans-serif',
                foreColor: '#6b7280',
            },
            dataLabels: {
                enabled: false
            },
            stroke: {
                width: [1, 4],
                curve: 'smooth'
            },
            xaxis: {
                categories: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
            },
            yaxis: [
                {
                    axisTicks: {
                        show: true,
                    },
                    axisBorder: {
                        show: true,
                        color: '#008FFB'
                    },
                    labels: {
                        style: {
                            colors: '#008FFB',
                        },
                        formatter: function(value) {
                            return '₹' + (value / 1000) + 'k';
                        }
                    },
                    title: {
                        text: "Revenue (₹)",
                        style: {
                            color: '#008FFB',
                        }
                    },
                },
                {
                    opposite: true,
                    axisTicks: {
                        show: true,
                    },
                    axisBorder: {
                        show: true,
                        color: '#00E396'
                    },
                    labels: {
                        style: {
                            colors: '#00E396',
                        }
                    },
                    title: {
                        text: "Orders",
                        style: {
                            color: '#00E396',
                        }
                    }
                },
            ],
            tooltip: {
                fixed: {
                    enabled: true,
                    position: 'topLeft',
                    offsetY: 30,
                    offsetX: 60
                },
            },
            legend: {
                horizontalAlign: 'left',
                offsetX: 40
            },
            colors: ['#3B82F6', '#10B981'],
            grid: {
                borderColor: '#f1f1f1',
                strokeDashArray: 5,
            }
        };

        var chart = new ApexCharts(document.querySelector("#performanceChart"), options);
        chart.render();

        // Update chart on window resize
        window.addEventListener('resize', function() {
            chart.updateOptions({
                chart: {
                    width: '100%'
                }
            });
        });
    });
</script>
@endpush

@endsection
