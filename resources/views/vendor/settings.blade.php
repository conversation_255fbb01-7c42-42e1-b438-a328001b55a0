@extends('layouts.vendor')

@section('title', 'Settings')

@push('page-styles')
<link rel="stylesheet" href="{{ asset('css/vendor/pages/settings.css') }}">
@endpush

@section('content')
<div x-data="vendorSettings()">
    <!-- Settings Header -->
    <div class="settings-header">
        <div class="settings-header-left">
            <h1 class="settings-title">Account Settings</h1>
            <p class="settings-subtitle">Manage your account preferences and store configuration</p>
        </div>
        <div class="settings-header-right">
            <button @click="resetSettings()" class="btn btn-secondary">
                <i class="fas fa-undo"></i>
                <span>Reset</span>
            </button>
            <button @click="saveSettings()" class="btn btn-primary">
                <i class="fas fa-save"></i>
                <span>Save Changes</span>
            </button>
        </div>
    </div>

    <!-- Settings Layout -->
    <div class="settings-layout">
        <!-- Settings Sidebar -->
        <div class="settings-sidebar">
            <nav class="settings-nav">
                <a @click="activeSection = 'profile'" :class="{ 'active': activeSection === 'profile' }" class="settings-nav-item">
                    <i class="settings-nav-icon fas fa-user"></i>
                    <span>Profile</span>
                </a>
                <a @click="activeSection = 'store'" :class="{ 'active': activeSection === 'store' }" class="settings-nav-item">
                    <i class="settings-nav-icon fas fa-store"></i>
                    <span>Store Settings</span>
                </a>
                <a @click="activeSection = 'notifications'" :class="{ 'active': activeSection === 'notifications' }" class="settings-nav-item">
                    <i class="settings-nav-icon fas fa-bell"></i>
                    <span>Notifications</span>
                </a>
                <a @click="activeSection = 'payment'" :class="{ 'active': activeSection === 'payment' }" class="settings-nav-item">
                    <i class="settings-nav-icon fas fa-credit-card"></i>
                    <span>Payment</span>
                </a>
                <a @click="activeSection = 'security'" :class="{ 'active': activeSection === 'security' }" class="settings-nav-item">
                    <i class="settings-nav-icon fas fa-shield-alt"></i>
                    <span>Security</span>
                </a>
                <a @click="activeSection = 'preferences'" :class="{ 'active': activeSection === 'preferences' }" class="settings-nav-item">
                    <i class="settings-nav-icon fas fa-cog"></i>
                    <span>Preferences</span>
                </a>
            </nav>
        </div>

        <!-- Settings Content -->
        <div class="settings-content">
            <!-- Profile Section -->
            <div x-show="activeSection === 'profile'" class="settings-section active">
                <div class="settings-section-header">
                    <h2 class="settings-section-title">Profile Information</h2>
                    <p class="settings-section-description">Update your personal information and profile details</p>
                </div>

                <div class="form-grid-2">
                    <div class="form-group">
                        <label class="form-label required">Full Name</label>
                        <input type="text" x-model="formData.name" class="form-input" placeholder="Enter your full name">
                        <div class="form-help">This name will be displayed on your store and invoices</div>
                    </div>

                    <div class="form-group">
                        <label class="form-label required">Email Address</label>
                        <input type="email" x-model="formData.email" class="form-input" placeholder="Enter your email">
                        <div class="form-help">We'll use this email for important account notifications</div>
                    </div>
                </div>

                <div class="form-grid-2">
                    <div class="form-group">
                        <label class="form-label">Phone Number</label>
                        <input type="tel" x-model="formData.phone" class="form-input" placeholder="+91 98765 43210">
                    </div>

                    <div class="form-group">
                        <label class="form-label">Date of Birth</label>
                        <input type="date" x-model="formData.dateOfBirth" class="form-input">
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label">Profile Picture</label>
                    <div class="file-upload">
                        <input type="file" class="file-upload-input" accept="image/*">
                        <label class="file-upload-label">
                            <i class="file-upload-icon fas fa-cloud-upload-alt"></i>
                            <span class="file-upload-text">Click to upload or drag and drop</span>
                            <span class="file-upload-hint">PNG, JPG up to 2MB</span>
                        </label>
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label">Bio</label>
                    <textarea x-model="formData.bio" class="form-textarea" placeholder="Tell us about yourself and your business..."></textarea>
                </div>
            </div>
            <!-- Store Settings Section -->
            <div x-show="activeSection === 'store'" class="settings-section">
                <div class="settings-section-header">
                    <h2 class="settings-section-title">Store Configuration</h2>
                    <p class="settings-section-description">Configure your store details and business information</p>
                </div>

                <div class="form-group">
                    <label class="form-label required">Store Name</label>
                    <input type="text" x-model="formData.storeName" class="form-input" placeholder="Enter your store name">
                </div>

                <div class="form-group">
                    <label class="form-label">Store Description</label>
                    <textarea x-model="formData.storeDescription" class="form-textarea" placeholder="Describe your store and what you sell..."></textarea>
                </div>

                <div class="form-grid-2">
                    <div class="form-group">
                        <label class="form-label">Store Category</label>
                        <select x-model="formData.storeCategory" class="form-select">
                            <option value="">Select Category</option>
                            <option value="electronics">Electronics</option>
                            <option value="clothing">Clothing & Fashion</option>
                            <option value="home">Home & Garden</option>
                            <option value="books">Books & Media</option>
                            <option value="sports">Sports & Fitness</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label class="form-label">Business Type</label>
                        <select x-model="formData.businessType" class="form-select">
                            <option value="individual">Individual</option>
                            <option value="partnership">Partnership</option>
                            <option value="company">Private Limited Company</option>
                            <option value="llp">Limited Liability Partnership</option>
                        </select>
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label">Store Address</label>
                    <textarea x-model="formData.storeAddress" class="form-textarea" placeholder="Enter your complete store address..."></textarea>
                </div>

                <div class="form-grid-2">
                    <div class="form-group">
                        <label class="form-label">GST Number</label>
                        <input type="text" x-model="formData.gstNumber" class="form-input" placeholder="Enter GST number">
                    </div>

                    <div class="form-group">
                        <label class="form-label">PAN Number</label>
                        <input type="text" x-model="formData.panNumber" class="form-input" placeholder="Enter PAN number">
                    </div>
                </div>
            </div>

            <!-- Notifications Section -->
            <div x-show="activeSection === 'notifications'" class="settings-section">
                <div class="settings-section-header">
                    <h2 class="settings-section-title">Notification Preferences</h2>
                    <p class="settings-section-description">Choose how you want to receive notifications</p>
                </div>

                <div class="form-group">
                    <div class="toggle-group">
                        <div class="toggle-info">
                            <div class="toggle-title">Email Notifications</div>
                            <div class="toggle-description">Receive important updates via email</div>
                        </div>
                        <label class="toggle-switch">
                            <input type="checkbox" x-model="formData.emailNotifications">
                            <span class="toggle-slider"></span>
                        </label>
                    </div>
                </div>

                <div class="form-group">
                    <div class="toggle-group">
                        <div class="toggle-info">
                            <div class="toggle-title">SMS Notifications</div>
                            <div class="toggle-description">Get instant alerts on your mobile</div>
                        </div>
                        <label class="toggle-switch">
                            <input type="checkbox" x-model="formData.smsNotifications">
                            <span class="toggle-slider"></span>
                        </label>
                    </div>
                </div>

                <div class="form-group">
                    <div class="toggle-group">
                        <div class="toggle-info">
                            <div class="toggle-title">Order Notifications</div>
                            <div class="toggle-description">Notify when new orders are received</div>
                        </div>
                        <label class="toggle-switch">
                            <input type="checkbox" x-model="formData.orderNotifications">
                            <span class="toggle-slider"></span>
                        </label>
                    </div>
                </div>

                <div class="form-group">
                    <div class="toggle-group">
                        <div class="toggle-info">
                            <div class="toggle-title">Marketing Updates</div>
                            <div class="toggle-description">Receive tips and promotional content</div>
                        </div>
                        <label class="toggle-switch">
                            <input type="checkbox" x-model="formData.marketingUpdates">
                            <span class="toggle-slider"></span>
                        </label>
                    </div>
                </div>
            </div>

            <!-- Security Section -->
            <div x-show="activeSection === 'security'" class="settings-section">
                <div class="settings-section-header">
                    <h2 class="settings-section-title">Security Settings</h2>
                    <p class="settings-section-description">Manage your password and security preferences</p>
                </div>

                <div class="form-group">
                    <label class="form-label required">Current Password</label>
                    <input type="password" x-model="formData.currentPassword" class="form-input" placeholder="Enter current password">
                </div>

                <div class="form-grid-2">
                    <div class="form-group">
                        <label class="form-label required">New Password</label>
                        <input type="password" x-model="formData.newPassword" class="form-input" placeholder="Enter new password">
                        <div class="form-help">Password must be at least 8 characters long</div>
                    </div>

                    <div class="form-group">
                        <label class="form-label required">Confirm Password</label>
                        <input type="password" x-model="formData.confirmPassword" class="form-input" placeholder="Confirm new password">
                    </div>
                </div>

                <div class="form-group">
                    <div class="toggle-group">
                        <div class="toggle-info">
                            <div class="toggle-title">Two-Factor Authentication</div>
                            <div class="toggle-description">Add an extra layer of security to your account</div>
                        </div>
                        <label class="toggle-switch">
                            <input type="checkbox" x-model="formData.twoFactorAuth">
                            <span class="toggle-slider"></span>
                        </label>
                    </div>
                </div>
            </div>

            <!-- Preferences Section -->
            <div x-show="activeSection === 'preferences'" class="settings-section">
                <div class="settings-section-header">
                    <h2 class="settings-section-title">General Preferences</h2>
                    <p class="settings-section-description">Customize your experience and regional settings</p>
                </div>

                <div class="form-grid-2">
                    <div class="form-group">
                        <label class="form-label">Language</label>
                        <select x-model="formData.language" class="form-select">
                            <option value="en">English</option>
                            <option value="hi">हिंदी (Hindi)</option>
                            <option value="bn">বাংলা (Bengali)</option>
                            <option value="te">తెలుగు (Telugu)</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label class="form-label">Currency</label>
                        <select x-model="formData.currency" class="form-select">
                            <option value="INR">₹ Indian Rupee (INR)</option>
                            <option value="USD">$ US Dollar (USD)</option>
                            <option value="EUR">€ Euro (EUR)</option>
                        </select>
                    </div>
                </div>

                <div class="form-grid-2">
                    <div class="form-group">
                        <label class="form-label">Timezone</label>
                        <select x-model="formData.timezone" class="form-select">
                            <option value="Asia/Kolkata">Asia/Kolkata (IST)</option>
                            <option value="Asia/Dubai">Asia/Dubai (GST)</option>
                            <option value="UTC">UTC</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label class="form-label">Date Format</label>
                        <select x-model="formData.dateFormat" class="form-select">
                            <option value="DD/MM/YYYY">DD/MM/YYYY</option>
                            <option value="MM/DD/YYYY">MM/DD/YYYY</option>
                            <option value="YYYY-MM-DD">YYYY-MM-DD</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Settings Actions -->
            <div class="settings-actions">
                <button @click="resetSettings()" class="btn btn-secondary">
                    <i class="fas fa-undo"></i>
                    <span>Reset to Default</span>
                </button>
                <button @click="saveSettings()" class="btn btn-primary">
                    <i class="fas fa-save"></i>
                    <span>Save All Changes</span>
                </button>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
function vendorSettings() {
    return {
        activeSection: 'profile',
        formData: {
            // Profile
            name: 'Rajesh Kumar',
            email: '<EMAIL>',
            phone: '+91 98765 43210',
            dateOfBirth: '1985-06-15',
            bio: 'Experienced vendor with 10+ years in electronics retail.',

            // Store
            storeName: 'Kumar Electronics',
            storeDescription: 'Your trusted electronics store with quality products and excellent service.',
            storeCategory: 'electronics',
            businessType: 'individual',
            storeAddress: '123 Main Street, Electronics Market, Mumbai, Maharashtra 400001',
            gstNumber: '27**********1Z5',
            panNumber: '**********',

            // Notifications
            emailNotifications: true,
            smsNotifications: true,
            orderNotifications: true,
            marketingUpdates: false,

            // Security
            currentPassword: '',
            newPassword: '',
            confirmPassword: '',
            twoFactorAuth: false,

            // Preferences
            language: 'en',
            currency: 'INR',
            timezone: 'Asia/Kolkata',
            dateFormat: 'DD/MM/YYYY'
        },

        init() {
            // Initialize settings
            this.loadSettings();
        },

        loadSettings() {
            // Load settings from server or localStorage
            console.log('Loading settings...');
        },

        saveSettings() {
            // Validate form data
            if (!this.validateForm()) {
                return;
            }

            // Save settings
            console.log('Saving settings:', this.formData);
            window.showToast('Settings saved successfully!', 'success');
        },

        resetSettings() {
            if (confirm('Are you sure you want to reset all settings to default?')) {
                this.loadSettings();
                window.showToast('Settings reset to default', 'info');
            }
        },

        validateForm() {
            // Basic validation
            if (this.activeSection === 'profile') {
                if (!this.formData.name || !this.formData.email) {
                    window.showToast('Please fill in all required fields', 'error');
                    return false;
                }
            }

            if (this.activeSection === 'security') {
                if (this.formData.newPassword && this.formData.newPassword !== this.formData.confirmPassword) {
                    window.showToast('Passwords do not match', 'error');
                    return false;
                }
            }

            return true;
        }
    }
}
</script>
@endpush
@endsection

