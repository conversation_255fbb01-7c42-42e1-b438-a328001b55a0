/* ===== MODERN VENDOR LAYOUT CSS ===== */

/* CSS Variables for Modern Design */
:root {
    /* Primary Colors */
    --vendor-primary: #6366f1;
    --vendor-primary-dark: #4f46e5;
    --vendor-primary-light: #818cf8;
    --vendor-primary-50: #eef2ff;
    --vendor-primary-100: #e0e7ff;
    --vendor-primary-500: #6366f1;
    --vendor-primary-600: #5b21b6;
    --vendor-primary-700: #4c1d95;
    
    /* Success Colors */
    --vendor-success: #10b981;
    --vendor-success-light: #34d399;
    --vendor-success-dark: #059669;
    
    /* Warning Colors */
    --vendor-warning: #f59e0b;
    --vendor-warning-light: #fbbf24;
    --vendor-warning-dark: #d97706;
    
    /* Error Colors */
    --vendor-error: #ef4444;
    --vendor-error-light: #f87171;
    --vendor-error-dark: #dc2626;
    
    /* Neutral Colors */
    --vendor-white: #ffffff;
    --vendor-gray-50: #f9fafb;
    --vendor-gray-100: #f3f4f6;
    --vendor-gray-200: #e5e7eb;
    --vendor-gray-300: #d1d5db;
    --vendor-gray-400: #9ca3af;
    --vendor-gray-500: #6b7280;
    --vendor-gray-600: #4b5563;
    --vendor-gray-700: #374151;
    --vendor-gray-800: #1f2937;
    --vendor-gray-900: #111827;
    
    /* Layout Variables */
    --vendor-sidebar-width: 280px;
    --vendor-sidebar-collapsed-width: 80px;
    --vendor-header-height: 80px;
    --vendor-border-radius: 12px;
    --vendor-border-radius-lg: 16px;
    --vendor-border-radius-xl: 20px;
    
    /* Shadows */
    --vendor-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --vendor-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --vendor-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --vendor-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --vendor-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    
    /* Transitions */
    --vendor-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --vendor-transition-fast: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
    
    /* Typography */
    --vendor-font-family: 'Inter', 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --vendor-font-size-xs: 0.75rem;
    --vendor-font-size-sm: 0.875rem;
    --vendor-font-size-base: 1rem;
    --vendor-font-size-lg: 1.125rem;
    --vendor-font-size-xl: 1.25rem;
    --vendor-font-size-2xl: 1.5rem;
    --vendor-font-size-3xl: 1.875rem;
}

/* Dark Mode Variables */
.dark {
    --vendor-bg: #0f172a;
    --vendor-surface: #1e293b;
    --vendor-surface-hover: #334155;
    --vendor-text: #f1f5f9;
    --vendor-text-muted: #94a3b8;
    --vendor-border: #334155;
}

/* Light Mode Variables */
:root {
    --vendor-bg: #f8fafc;
    --vendor-surface: #ffffff;
    --vendor-surface-hover: #f1f5f9;
    --vendor-text: #1e293b;
    --vendor-text-muted: #64748b;
    --vendor-border: #e2e8f0;
}

/* Global Styles */
* {
    box-sizing: border-box;
}

.vendor-body {
    font-family: var(--vendor-font-family);
    background: var(--vendor-bg);
    color: var(--vendor-text);
    margin: 0;
    padding: 0;
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Loading Screen */
.vendor-loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, var(--vendor-primary), var(--vendor-primary-dark));
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-content {
    text-align: center;
    color: white;
}

.loading-logo {
    margin-bottom: 2rem;
}

.loading-logo img {
    height: 60px;
    width: auto;
}

.loading-logo-fallback {
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: var(--vendor-border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    font-weight: 700;
    margin: 0 auto;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-top: 3px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    font-size: var(--vendor-font-size-lg);
    font-weight: 500;
    opacity: 0.9;
}

/* Main App Container */
.vendor-app {
    display: flex;
    min-height: 100vh;
    background: var(--vendor-bg);
}

/* Mobile Overlay */
.vendor-mobile-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 40;
    display: none;
}

@media (max-width: 1024px) {
    .vendor-mobile-overlay {
        display: block;
    }
}

/* Utility Classes */
.desktop-only {
    display: block;
}

.mobile-only {
    display: none;
}

@media (max-width: 1024px) {
    .desktop-only {
        display: none;
    }
    
    .mobile-only {
        display: block;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    :root {
        --vendor-sidebar-width: 260px;
        --vendor-header-height: 70px;
    }
}

@media (max-width: 480px) {
    :root {
        --vendor-sidebar-width: 240px;
        --vendor-header-height: 65px;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.slide-in-left {
    animation: slideInLeft 0.3s ease-out;
}

@keyframes slideInLeft {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

.slide-in-right {
    animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
    from { transform: translateX(100%); }
    to { transform: translateX(0); }
}

.bounce-in {
    animation: bounceIn 0.6s ease-out;
}

@keyframes bounceIn {
    0% { transform: scale(0.3); opacity: 0; }
    50% { transform: scale(1.05); }
    70% { transform: scale(0.9); }
    100% { transform: scale(1); opacity: 1; }
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: var(--vendor-gray-100);
}

::-webkit-scrollbar-thumb {
    background: var(--vendor-gray-300);
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--vendor-gray-400);
}

.dark ::-webkit-scrollbar-track {
    background: var(--vendor-gray-800);
}

.dark ::-webkit-scrollbar-thumb {
    background: var(--vendor-gray-600);
}

.dark ::-webkit-scrollbar-thumb:hover {
    background: var(--vendor-gray-500);
}

/* ===== SIDEBAR STYLES ===== */
.vendor-sidebar {
    width: var(--vendor-sidebar-width);
    background: var(--vendor-surface);
    border-right: 1px solid var(--vendor-border);
    display: flex;
    flex-direction: column;
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    z-index: 50;
    transition: var(--vendor-transition);
    box-shadow: var(--vendor-shadow-lg);
}

.vendor-sidebar.sidebar-collapsed {
    width: var(--vendor-sidebar-collapsed-width);
}

.vendor-sidebar.sidebar-mobile-open {
    transform: translateX(0);
}

@media (max-width: 1024px) {
    .vendor-sidebar {
        transform: translateX(-100%);
    }
}

@media (min-width: 1025px) {
    .vendor-sidebar {
        position: relative;
        transform: translateX(0);
    }
}

/* Sidebar Header */
.sidebar-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1.5rem 1.25rem;
    border-bottom: 1px solid var(--vendor-border);
    min-height: var(--vendor-header-height);
}

.sidebar-brand {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    cursor: pointer;
    transition: var(--vendor-transition-fast);
}

.sidebar-brand:hover {
    transform: translateY(-1px);
}

.brand-logo {
    position: relative;
}

.brand-logo-img {
    height: 40px;
    width: auto;
    border-radius: var(--vendor-border-radius);
}

.brand-logo-fallback {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--vendor-primary), var(--vendor-primary-dark));
    color: white;
    border-radius: var(--vendor-border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    font-weight: 700;
}

.brand-text {
    display: flex;
    flex-direction: column;
}

.brand-title {
    font-size: var(--vendor-font-size-xl);
    font-weight: 700;
    color: var(--vendor-text);
    margin: 0;
    line-height: 1.2;
}

.brand-subtitle {
    font-size: var(--vendor-font-size-xs);
    color: var(--vendor-text-muted);
    margin: 0;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.sidebar-controls {
    display: flex;
    gap: 0.5rem;
}

.sidebar-toggle-btn,
.sidebar-close-btn {
    width: 36px;
    height: 36px;
    border: none;
    background: var(--vendor-gray-100);
    color: var(--vendor-text-muted);
    border-radius: var(--vendor-border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--vendor-transition-fast);
}

.sidebar-toggle-btn:hover,
.sidebar-close-btn:hover {
    background: var(--vendor-primary-100);
    color: var(--vendor-primary);
    transform: scale(1.05);
}

.dark .sidebar-toggle-btn,
.dark .sidebar-close-btn {
    background: var(--vendor-gray-700);
    color: var(--vendor-gray-400);
}

.dark .sidebar-toggle-btn:hover,
.dark .sidebar-close-btn:hover {
    background: var(--vendor-primary-700);
    color: var(--vendor-primary-light);
}

/* Sidebar Navigation */
.sidebar-nav {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    padding: 1rem 0;
}

.nav-section {
    margin-bottom: 2rem;
}

.nav-section:last-child {
    margin-bottom: 0;
}

.nav-section-title {
    padding: 0 1.25rem 0.75rem;
    font-size: var(--vendor-font-size-xs);
    font-weight: 600;
    color: var(--vendor-text-muted);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.nav-items {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    padding: 0 0.75rem;
}

.nav-item {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    color: var(--vendor-text-muted);
    text-decoration: none;
    border-radius: var(--vendor-border-radius);
    font-weight: 500;
    font-size: var(--vendor-font-size-sm);
    transition: var(--vendor-transition-fast);
    position: relative;
    gap: 0.75rem;
}

.nav-item:hover {
    background: var(--vendor-surface-hover);
    color: var(--vendor-text);
    transform: translateX(4px);
}

.nav-item.active {
    background: linear-gradient(135deg, var(--vendor-primary-100), var(--vendor-primary-50));
    color: var(--vendor-primary);
    font-weight: 600;
}

.dark .nav-item.active {
    background: linear-gradient(135deg, var(--vendor-primary-700), var(--vendor-primary-600));
    color: var(--vendor-primary-light);
}

.nav-item-icon {
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.nav-item-text {
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.nav-item-badge {
    background: var(--vendor-error);
    color: white;
    font-size: var(--vendor-font-size-xs);
    font-weight: 600;
    padding: 0.125rem 0.5rem;
    border-radius: 9999px;
    min-width: 20px;
    text-align: center;
}

.nav-item-indicator {
    position: absolute;
    left: -0.75rem;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 0;
    background: var(--vendor-primary);
    border-radius: 0 2px 2px 0;
    transition: var(--vendor-transition-fast);
}

.nav-item.active .nav-item-indicator {
    height: 24px;
}

/* Sidebar Footer */
.sidebar-footer {
    padding: 1rem 1.25rem;
    border-top: 1px solid var(--vendor-border);
    background: var(--vendor-surface);
}

.user-profile {
    position: relative;
}

.user-profile-btn {
    width: 100%;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    background: transparent;
    border: none;
    border-radius: var(--vendor-border-radius);
    cursor: pointer;
    transition: var(--vendor-transition-fast);
    text-align: left;
}

.user-profile-btn:hover {
    background: var(--vendor-surface-hover);
}

.user-avatar {
    position: relative;
    flex-shrink: 0;
}

.user-avatar-img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

.user-status-indicator {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 12px;
    height: 12px;
    background: var(--vendor-success);
    border: 2px solid var(--vendor-surface);
    border-radius: 50%;
}

.user-info {
    flex: 1;
    min-width: 0;
}

.user-name {
    font-size: var(--vendor-font-size-sm);
    font-weight: 600;
    color: var(--vendor-text);
    margin: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.user-role {
    font-size: var(--vendor-font-size-xs);
    color: var(--vendor-text-muted);
    margin: 0;
}

.user-dropdown-icon {
    color: var(--vendor-text-muted);
    transition: var(--vendor-transition-fast);
}

.user-dropdown-icon.rotate-180 {
    transform: rotate(180deg);
}

.user-dropdown-menu {
    position: absolute;
    bottom: 100%;
    left: 0;
    right: 0;
    background: var(--vendor-surface);
    border: 1px solid var(--vendor-border);
    border-radius: var(--vendor-border-radius);
    box-shadow: var(--vendor-shadow-lg);
    margin-bottom: 0.5rem;
    overflow: hidden;
}

.dropdown-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    color: var(--vendor-text);
    text-decoration: none;
    font-size: var(--vendor-font-size-sm);
    transition: var(--vendor-transition-fast);
    border: none;
    background: transparent;
    width: 100%;
    text-align: left;
    cursor: pointer;
}

.dropdown-item:hover {
    background: var(--vendor-surface-hover);
}

.dropdown-divider {
    height: 1px;
    background: var(--vendor-border);
    margin: 0.5rem 0;
}

.dropdown-form {
    margin: 0;
}

.logout-btn {
    color: var(--vendor-error) !important;
}

.logout-btn:hover {
    background: var(--vendor-error) !important;
    color: white !important;
}

/* ===== HEADER STYLES ===== */
.vendor-main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-width: 0;
    margin-left: var(--vendor-sidebar-width);
    transition: var(--vendor-transition);
}

.vendor-sidebar.sidebar-collapsed + .vendor-main-content {
    margin-left: var(--vendor-sidebar-collapsed-width);
}

@media (max-width: 1024px) {
    .vendor-main-content {
        margin-left: 0;
    }
}

.vendor-header {
    background: var(--vendor-surface);
    border-bottom: 1px solid var(--vendor-border);
    height: var(--vendor-header-height);
    display: flex;
    align-items: center;
    position: sticky;
    top: 0;
    z-index: 30;
    box-shadow: var(--vendor-shadow-sm);
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: 0 2rem;
    gap: 2rem;
}

@media (max-width: 768px) {
    .header-content {
        padding: 0 1rem;
        gap: 1rem;
    }
}

/* Header Left Section */
.header-left {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex: 1;
    min-width: 0;
}

.mobile-menu-btn {
    width: 40px;
    height: 40px;
    border: none;
    background: var(--vendor-gray-100);
    color: var(--vendor-text);
    border-radius: var(--vendor-border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--vendor-transition-fast);
}

.mobile-menu-btn:hover {
    background: var(--vendor-primary-100);
    color: var(--vendor-primary);
}

.page-title-section {
    flex: 1;
    min-width: 0;
}

.page-title {
    font-size: var(--vendor-font-size-2xl);
    font-weight: 700;
    color: var(--vendor-text);
    margin: 0;
    line-height: 1.2;
}

@media (max-width: 768px) {
    .page-title {
        font-size: var(--vendor-font-size-xl);
    }
}

.breadcrumb {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-top: 0.25rem;
}

.breadcrumb-item {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    font-size: var(--vendor-font-size-sm);
    color: var(--vendor-text-muted);
}

.breadcrumb-item.current {
    color: var(--vendor-primary);
    font-weight: 500;
}

.breadcrumb-separator {
    color: var(--vendor-text-muted);
    font-size: var(--vendor-font-size-xs);
}
