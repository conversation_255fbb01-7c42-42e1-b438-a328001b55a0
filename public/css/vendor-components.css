/* ===== VENDOR COMPONENTS CSS ===== */

/* Header Center - Search Component */
.header-center {
    flex: 2;
    max-width: 600px;
    margin: 0 auto;
}

@media (max-width: 768px) {
    .header-center {
        display: none;
    }
}

.search-container {
    position: relative;
    width: 100%;
}

.search-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    background: var(--vendor-gray-50);
    border: 2px solid transparent;
    border-radius: var(--vendor-border-radius-lg);
    transition: var(--vendor-transition-fast);
    overflow: hidden;
}

.search-input-wrapper.focused {
    background: var(--vendor-white);
    border-color: var(--vendor-primary);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.dark .search-input-wrapper {
    background: var(--vendor-gray-800);
}

.dark .search-input-wrapper.focused {
    background: var(--vendor-gray-700);
    border-color: var(--vendor-primary-light);
}

.search-icon {
    position: absolute;
    left: 1rem;
    color: var(--vendor-text-muted);
    font-size: var(--vendor-font-size-sm);
    z-index: 2;
}

.search-input {
    width: 100%;
    padding: 0.875rem 1rem 0.875rem 2.75rem;
    border: none;
    background: transparent;
    color: var(--vendor-text);
    font-size: var(--vendor-font-size-sm);
    outline: none;
    font-family: var(--vendor-font-family);
}

.search-input::placeholder {
    color: var(--vendor-text-muted);
}

.search-clear-btn {
    position: absolute;
    right: 0.75rem;
    width: 24px;
    height: 24px;
    border: none;
    background: var(--vendor-gray-200);
    color: var(--vendor-text-muted);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--vendor-transition-fast);
    font-size: 0.75rem;
}

.search-clear-btn:hover {
    background: var(--vendor-gray-300);
    color: var(--vendor-text);
}

.search-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--vendor-surface);
    border: 1px solid var(--vendor-border);
    border-radius: var(--vendor-border-radius);
    box-shadow: var(--vendor-shadow-lg);
    margin-top: 0.5rem;
    overflow: hidden;
    z-index: 50;
}

.search-suggestion-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    color: var(--vendor-text);
    cursor: pointer;
    transition: var(--vendor-transition-fast);
}

.search-suggestion-item:hover {
    background: var(--vendor-surface-hover);
}

/* Header Right Section */
.header-right {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.quick-actions {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

@media (max-width: 768px) {
    .quick-actions {
        display: none;
    }
}

.quick-action-btn {
    position: relative;
    width: 40px;
    height: 40px;
    border: none;
    background: var(--vendor-gray-100);
    color: var(--vendor-text-muted);
    border-radius: var(--vendor-border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--vendor-transition-fast);
}

.quick-action-btn:hover {
    background: var(--vendor-primary-100);
    color: var(--vendor-primary);
    transform: translateY(-1px);
}

.action-badge {
    position: absolute;
    top: -4px;
    right: -4px;
    background: var(--vendor-error);
    color: white;
    font-size: 0.625rem;
    font-weight: 600;
    padding: 0.125rem 0.375rem;
    border-radius: 9999px;
    min-width: 18px;
    text-align: center;
    line-height: 1;
}

/* Theme Toggle */
.theme-toggle-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 0;
}

.theme-toggle-track {
    width: 50px;
    height: 26px;
    background: var(--vendor-gray-200);
    border-radius: 13px;
    position: relative;
    transition: var(--vendor-transition);
}

.theme-toggle-track.active {
    background: var(--vendor-primary);
}

.theme-toggle-thumb {
    position: absolute;
    top: 2px;
    left: 2px;
    width: 22px;
    height: 22px;
    background: white;
    border-radius: 50%;
    transition: var(--vendor-transition);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--vendor-shadow-sm);
}

.theme-toggle-track.active .theme-toggle-thumb {
    transform: translateX(24px);
}

.light-icon,
.dark-icon {
    position: absolute;
    font-size: 0.75rem;
    transition: var(--vendor-transition-fast);
    opacity: 0;
}

.light-icon.active {
    opacity: 1;
    color: #fbbf24;
}

.dark-icon.active {
    opacity: 1;
    color: #6366f1;
}

/* Notifications Component */
.notifications-container {
    position: relative;
}

.notifications-btn {
    position: relative;
    width: 44px;
    height: 44px;
    border: none;
    background: var(--vendor-gray-100);
    border-radius: var(--vendor-border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--vendor-transition-fast);
}

.notifications-btn:hover {
    background: var(--vendor-primary-100);
    transform: translateY(-1px);
}

.notifications-icon {
    position: relative;
    color: var(--vendor-text-muted);
    font-size: 1.125rem;
}

.notifications-btn:hover .notifications-icon {
    color: var(--vendor-primary);
}

.notifications-count {
    position: absolute;
    top: -6px;
    right: -6px;
    background: var(--vendor-error);
    color: white;
    font-size: 0.625rem;
    font-weight: 700;
    padding: 0.125rem 0.375rem;
    border-radius: 9999px;
    min-width: 18px;
    text-align: center;
    line-height: 1;
}

.notifications-pulse {
    position: absolute;
    top: -6px;
    right: -6px;
    width: 18px;
    height: 18px;
    background: var(--vendor-error);
    border-radius: 50%;
    animation: pulse 2s infinite;
    opacity: 0.7;
}

@keyframes pulse {
    0% {
        transform: scale(0.95);
        box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7);
    }
    
    70% {
        transform: scale(1);
        box-shadow: 0 0 0 10px rgba(239, 68, 68, 0);
    }
    
    100% {
        transform: scale(0.95);
        box-shadow: 0 0 0 0 rgba(239, 68, 68, 0);
    }
}

.notifications-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    width: 380px;
    background: var(--vendor-surface);
    border: 1px solid var(--vendor-border);
    border-radius: var(--vendor-border-radius-lg);
    box-shadow: var(--vendor-shadow-xl);
    margin-top: 0.5rem;
    overflow: hidden;
    z-index: 50;
}

@media (max-width: 480px) {
    .notifications-dropdown {
        width: 320px;
        right: -50px;
    }
}

.notifications-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 1.25rem;
    border-bottom: 1px solid var(--vendor-border);
    background: var(--vendor-gray-50);
}

.dark .notifications-header {
    background: var(--vendor-gray-800);
}

.notifications-title {
    font-size: var(--vendor-font-size-lg);
    font-weight: 600;
    color: var(--vendor-text);
    margin: 0;
}

.mark-all-read-btn {
    background: none;
    border: none;
    color: var(--vendor-primary);
    font-size: var(--vendor-font-size-sm);
    font-weight: 500;
    cursor: pointer;
    transition: var(--vendor-transition-fast);
}

.mark-all-read-btn:hover {
    color: var(--vendor-primary-dark);
}

.notifications-list {
    max-height: 400px;
    overflow-y: auto;
}

.notification-item {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    padding: 1rem 1.25rem;
    border-bottom: 1px solid var(--vendor-border);
    transition: var(--vendor-transition-fast);
    cursor: pointer;
}

.notification-item:hover {
    background: var(--vendor-surface-hover);
}

.notification-item:last-child {
    border-bottom: none;
}

.notification-item.unread {
    background: var(--vendor-primary-50);
}

.dark .notification-item.unread {
    background: rgba(99, 102, 241, 0.1);
}

.notification-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    font-size: 1rem;
}

.notification-icon.order {
    background: var(--vendor-success);
    color: white;
}

.notification-icon.message {
    background: var(--vendor-primary);
    color: white;
}

.notification-icon.success {
    background: var(--vendor-success);
    color: white;
}

.notification-content {
    flex: 1;
    min-width: 0;
}

.notification-title {
    font-size: var(--vendor-font-size-sm);
    font-weight: 600;
    color: var(--vendor-text);
    margin: 0 0 0.25rem 0;
}

.notification-message {
    font-size: var(--vendor-font-size-sm);
    color: var(--vendor-text-muted);
    margin: 0 0 0.5rem 0;
    line-height: 1.4;
}

.notification-time {
    font-size: var(--vendor-font-size-xs);
    color: var(--vendor-text-muted);
}

.notification-actions {
    display: flex;
    gap: 0.5rem;
}

.notification-action-btn {
    background: var(--vendor-primary);
    color: white;
    border: none;
    padding: 0.25rem 0.75rem;
    border-radius: var(--vendor-border-radius);
    font-size: var(--vendor-font-size-xs);
    font-weight: 500;
    cursor: pointer;
    transition: var(--vendor-transition-fast);
}

.notification-action-btn:hover {
    background: var(--vendor-primary-dark);
}

.notifications-footer {
    padding: 1rem 1.25rem;
    border-top: 1px solid var(--vendor-border);
    background: var(--vendor-gray-50);
    text-align: center;
}

.dark .notifications-footer {
    background: var(--vendor-gray-800);
}

.view-all-notifications {
    color: var(--vendor-primary);
    text-decoration: none;
    font-size: var(--vendor-font-size-sm);
    font-weight: 500;
    transition: var(--vendor-transition-fast);
}

.view-all-notifications:hover {
    color: var(--vendor-primary-dark);
}

/* User Profile Component */
.user-profile-container {
    position: relative;
}

.user-profile-btn {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.5rem;
    background: transparent;
    border: none;
    border-radius: var(--vendor-border-radius);
    cursor: pointer;
    transition: var(--vendor-transition-fast);
}

.user-profile-btn:hover {
    background: var(--vendor-surface-hover);
}

.user-avatar-wrapper {
    position: relative;
}

.user-avatar-img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid var(--vendor-border);
}

.user-status-dot {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 12px;
    height: 12px;
    background: var(--vendor-success);
    border: 2px solid var(--vendor-surface);
    border-radius: 50%;
}

.user-info {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    min-width: 0;
}

.user-name {
    font-size: var(--vendor-font-size-sm);
    font-weight: 600;
    color: var(--vendor-text);
    margin: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 120px;
}

.user-role {
    font-size: var(--vendor-font-size-xs);
    color: var(--vendor-text-muted);
    margin: 0;
}

.user-dropdown-arrow {
    color: var(--vendor-text-muted);
    transition: var(--vendor-transition-fast);
}

.user-dropdown-arrow .fa-chevron-down.rotate-180 {
    transform: rotate(180deg);
}

.user-profile-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    width: 280px;
    background: var(--vendor-surface);
    border: 1px solid var(--vendor-border);
    border-radius: var(--vendor-border-radius-lg);
    box-shadow: var(--vendor-shadow-xl);
    margin-top: 0.5rem;
    overflow: hidden;
    z-index: 50;
}

.profile-dropdown-header {
    padding: 1.25rem;
    background: linear-gradient(135deg, var(--vendor-primary), var(--vendor-primary-dark));
    color: white;
}

.profile-avatar {
    width: 60px;
    height: 60px;
    margin: 0 auto 1rem;
    border-radius: 50%;
    overflow: hidden;
    border: 3px solid rgba(255, 255, 255, 0.2);
}

.profile-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.profile-info {
    text-align: center;
}

.profile-name {
    font-size: var(--vendor-font-size-lg);
    font-weight: 600;
    margin: 0 0 0.25rem 0;
    color: white;
}

.profile-email {
    font-size: var(--vendor-font-size-sm);
    margin: 0 0 0.75rem 0;
    color: rgba(255, 255, 255, 0.8);
}

.profile-badge {
    display: inline-block;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    font-size: var(--vendor-font-size-xs);
    font-weight: 500;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.profile-dropdown-menu {
    padding: 0.5rem 0;
}

.profile-menu-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1.25rem;
    color: var(--vendor-text);
    text-decoration: none;
    font-size: var(--vendor-font-size-sm);
    transition: var(--vendor-transition-fast);
    border: none;
    background: transparent;
    width: 100%;
    text-align: left;
    cursor: pointer;
}

.profile-menu-item:hover {
    background: var(--vendor-surface-hover);
}

.profile-menu-item i {
    width: 16px;
    text-align: center;
    color: var(--vendor-text-muted);
}

.profile-menu-divider {
    height: 1px;
    background: var(--vendor-border);
    margin: 0.5rem 0;
}

.profile-logout-form {
    margin: 0;
}

.logout-item {
    color: var(--vendor-error) !important;
}

.logout-item:hover {
    background: var(--vendor-error) !important;
    color: white !important;
}

.logout-item:hover i {
    color: white !important;
}

/* Main Content Area */
.vendor-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: calc(100vh - var(--vendor-header-height));
    background: var(--vendor-bg);
}

.page-header-actions {
    padding: 1rem 2rem 0;
    border-bottom: 1px solid var(--vendor-border);
    background: var(--vendor-surface);
}

@media (max-width: 768px) {
    .page-header-actions {
        padding: 1rem 1rem 0;
    }
}

.page-content {
    flex: 1;
    padding: 2rem;
    overflow-y: auto;
}

@media (max-width: 768px) {
    .page-content {
        padding: 1rem;
    }
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: 2rem;
    right: 2rem;
    z-index: 9999;
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    max-width: 400px;
}

@media (max-width: 768px) {
    .toast-container {
        top: 1rem;
        right: 1rem;
        left: 1rem;
        max-width: none;
    }
}

.toast {
    background: var(--vendor-surface);
    border: 1px solid var(--vendor-border);
    border-radius: var(--vendor-border-radius);
    box-shadow: var(--vendor-shadow-lg);
    overflow: hidden;
    animation: slideInRight 0.3s ease-out;
}

.toast-success {
    border-left: 4px solid var(--vendor-success);
}

.toast-error {
    border-left: 4px solid var(--vendor-error);
}

.toast-info {
    border-left: 4px solid var(--vendor-primary);
}

.toast-warning {
    border-left: 4px solid var(--vendor-warning);
}

.toast-content {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
}

.toast-icon {
    flex-shrink: 0;
    font-size: 1.125rem;
}

.toast-success .toast-icon {
    color: var(--vendor-success);
}

.toast-error .toast-icon {
    color: var(--vendor-error);
}

.toast-info .toast-icon {
    color: var(--vendor-primary);
}

.toast-warning .toast-icon {
    color: var(--vendor-warning);
}

.toast-message {
    flex: 1;
    font-size: var(--vendor-font-size-sm);
    color: var(--vendor-text);
    line-height: 1.4;
}

.toast-close {
    background: none;
    border: none;
    color: var(--vendor-text-muted);
    cursor: pointer;
    padding: 0.25rem;
    border-radius: var(--vendor-border-radius);
    transition: var(--vendor-transition-fast);
}

.toast-close:hover {
    background: var(--vendor-surface-hover);
    color: var(--vendor-text);
}

/* Utility Classes */
.card {
    background: var(--vendor-surface);
    border: 1px solid var(--vendor-border);
    border-radius: var(--vendor-border-radius-lg);
    box-shadow: var(--vendor-shadow);
    overflow: hidden;
}

.card-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--vendor-border);
    background: var(--vendor-gray-50);
}

.dark .card-header {
    background: var(--vendor-gray-800);
}

.card-body {
    padding: 1.5rem;
}

.card-footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid var(--vendor-border);
    background: var(--vendor-gray-50);
}

.dark .card-footer {
    background: var(--vendor-gray-800);
}

.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: var(--vendor-border-radius);
    font-size: var(--vendor-font-size-sm);
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: var(--vendor-transition-fast);
    line-height: 1;
}

.btn-primary {
    background: var(--vendor-primary);
    color: white;
}

.btn-primary:hover {
    background: var(--vendor-primary-dark);
    transform: translateY(-1px);
}

.btn-secondary {
    background: var(--vendor-gray-100);
    color: var(--vendor-text);
}

.btn-secondary:hover {
    background: var(--vendor-gray-200);
}

.btn-success {
    background: var(--vendor-success);
    color: white;
}

.btn-success:hover {
    background: var(--vendor-success-dark);
}

.btn-outline {
    background: transparent;
    border: 2px solid var(--vendor-primary);
    color: var(--vendor-primary);
}

.btn-outline:hover {
    background: var(--vendor-primary);
    color: white;
}
