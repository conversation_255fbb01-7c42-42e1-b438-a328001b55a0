/* ===== MODERN VENDOR COMPONENTS CSS ===== */

/* === MODERN HEADER === */
.modern-header {
    background: var(--vendor-surface-primary);
    border-bottom: 1px solid var(--vendor-border-primary);
    height: 80px;
    position: sticky;
    top: 0;
    z-index: var(--vendor-z-sticky);
    box-shadow: var(--vendor-shadow-sm);
    backdrop-filter: blur(10px);
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 var(--vendor-space-8);
    height: 100%;
    max-width: 1600px;
    margin: 0 auto;
}

@media (max-width: 1024px) {
    .header-content {
        padding: 0 var(--vendor-space-4);
    }
}

/* === HEADER LEFT === */
.header-left {
    display: flex;
    align-items: center;
    gap: var(--vendor-space-4);
    flex: 1;
}

.mobile-menu-btn {
    display: none;
    width: 40px;
    height: 40px;
    border: none;
    background: var(--vendor-surface-hover);
    border-radius: var(--vendor-radius-lg);
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--vendor-transition-fast);
    color: var(--vendor-text-secondary);
}

.mobile-menu-btn:hover {
    background: var(--vendor-brand-primary-100);
    color: var(--vendor-brand-primary);
}

@media (max-width: 1024px) {
    .mobile-menu-btn {
        display: flex;
    }
}

.page-title-section {
    flex: 1;
}

.page-title {
    font-size: var(--vendor-text-2xl);
    font-weight: var(--vendor-font-bold);
    color: var(--vendor-text-primary);
    margin: 0 0 var(--vendor-space-1);
    line-height: var(--vendor-leading-tight);
}

.breadcrumb {
    display: flex;
    align-items: center;
    gap: var(--vendor-space-2);
    font-size: var(--vendor-text-sm);
}

.breadcrumb-item {
    display: flex;
    align-items: center;
    gap: var(--vendor-space-1);
    color: var(--vendor-text-tertiary);
}

.breadcrumb-item.current {
    color: var(--vendor-text-secondary);
    font-weight: var(--vendor-font-medium);
}

.breadcrumb-separator {
    color: var(--vendor-text-tertiary);
    font-size: var(--vendor-text-xs);
}

@media (max-width: 768px) {
    .breadcrumb {
        display: none;
    }
}

/* === HEADER RIGHT === */
.header-right {
    display: flex;
    align-items: center;
    gap: var(--vendor-space-4);
}

/* === QUICK ACTIONS === */
.quick-actions {
    display: flex;
    align-items: center;
    gap: var(--vendor-space-2);
}

.quick-action-btn {
    position: relative;
    width: 40px;
    height: 40px;
    border: none;
    background: var(--vendor-surface-hover);
    border-radius: var(--vendor-radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--vendor-transition-fast);
    color: var(--vendor-text-secondary);
}

.quick-action-btn:hover {
    background: var(--vendor-brand-primary-100);
    color: var(--vendor-brand-primary);
    transform: translateY(-1px);
}

.action-badge {
    position: absolute;
    top: -2px;
    right: -2px;
    background: var(--vendor-error);
    color: var(--vendor-white);
    font-size: var(--vendor-text-xs);
    font-weight: var(--vendor-font-semibold);
    padding: var(--vendor-space-0-5) var(--vendor-space-1-5);
    border-radius: var(--vendor-radius-full);
    min-width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid var(--vendor-surface-primary);
}

/* === USER PROFILE === */
.user-profile-container {
    position: relative;
}

.user-profile-btn {
    display: flex;
    align-items: center;
    gap: var(--vendor-space-3);
    padding: var(--vendor-space-2);
    border: none;
    background: transparent;
    border-radius: var(--vendor-radius-lg);
    cursor: pointer;
    transition: var(--vendor-transition-fast);
}

.user-profile-btn:hover {
    background: var(--vendor-surface-hover);
}

.user-avatar-wrapper {
    position: relative;
}

.user-avatar-img {
    width: 36px;
    height: 36px;
    border-radius: var(--vendor-radius-full);
    object-fit: cover;
    border: 2px solid var(--vendor-border-primary);
}

.user-status-dot {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 10px;
    height: 10px;
    background: var(--vendor-success);
    border: 2px solid var(--vendor-surface-primary);
    border-radius: var(--vendor-radius-full);
}

.user-info {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    text-align: left;
}

.user-name {
    font-size: var(--vendor-text-sm);
    font-weight: var(--vendor-font-semibold);
    color: var(--vendor-text-primary);
    line-height: var(--vendor-leading-tight);
}

.user-role {
    font-size: var(--vendor-text-xs);
    color: var(--vendor-text-tertiary);
    line-height: var(--vendor-leading-tight);
}

.user-dropdown-arrow {
    color: var(--vendor-text-tertiary);
    transition: var(--vendor-transition-fast);
}

.user-dropdown-arrow i.rotate-180 {
    transform: rotate(180deg);
}

@media (max-width: 768px) {
    .user-info {
        display: none;
    }
    
    .user-dropdown-arrow {
        display: none;
    }
}

/* === USER PROFILE DROPDOWN === */
.user-profile-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    margin-top: var(--vendor-space-2);
    width: 280px;
    background: var(--vendor-surface-primary);
    border: 1px solid var(--vendor-border-primary);
    border-radius: var(--vendor-radius-xl);
    box-shadow: var(--vendor-shadow-xl);
    z-index: var(--vendor-z-dropdown);
    overflow: hidden;
}

.profile-dropdown-header {
    padding: var(--vendor-space-6);
    background: linear-gradient(135deg, var(--vendor-brand-primary-50), var(--vendor-brand-primary-100));
    border-bottom: 1px solid var(--vendor-border-primary);
    display: flex;
    align-items: center;
    gap: var(--vendor-space-4);
}

.profile-avatar img {
    width: 48px;
    height: 48px;
    border-radius: var(--vendor-radius-full);
    object-fit: cover;
    border: 2px solid var(--vendor-brand-primary-200);
}

.profile-info {
    flex: 1;
}

.profile-name {
    font-size: var(--vendor-text-base);
    font-weight: var(--vendor-font-semibold);
    color: var(--vendor-text-primary);
    margin: 0 0 var(--vendor-space-1);
}

.profile-email {
    font-size: var(--vendor-text-sm);
    color: var(--vendor-text-secondary);
    margin: 0 0 var(--vendor-space-2);
}

.profile-badge {
    display: inline-flex;
    align-items: center;
    padding: var(--vendor-space-1) var(--vendor-space-2);
    background: var(--vendor-brand-primary);
    color: var(--vendor-white);
    font-size: var(--vendor-text-xs);
    font-weight: var(--vendor-font-medium);
    border-radius: var(--vendor-radius-full);
}

.profile-dropdown-menu {
    padding: var(--vendor-space-2);
}

.profile-menu-item {
    display: flex;
    align-items: center;
    gap: var(--vendor-space-3);
    padding: var(--vendor-space-3) var(--vendor-space-4);
    border: none;
    background: transparent;
    border-radius: var(--vendor-radius-lg);
    color: var(--vendor-text-secondary);
    text-decoration: none;
    transition: var(--vendor-transition-fast);
    width: 100%;
    text-align: left;
    cursor: pointer;
    font-size: var(--vendor-text-sm);
    font-weight: var(--vendor-font-medium);
}

.profile-menu-item:hover {
    background: var(--vendor-surface-hover);
    color: var(--vendor-text-primary);
}

.profile-menu-item i {
    width: 16px;
    text-align: center;
    color: var(--vendor-text-tertiary);
}

.profile-menu-divider {
    height: 1px;
    background: var(--vendor-border-primary);
    margin: var(--vendor-space-2) 0;
}

.profile-logout-form {
    margin: 0;
}

.logout-item {
    color: var(--vendor-error);
}

.logout-item:hover {
    background: var(--vendor-error-light);
    color: var(--vendor-error-dark);
}

.logout-item i {
    color: var(--vendor-error);
}

/* === MAIN CONTENT === */
.modern-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: calc(100vh - 80px);
}

.page-content {
    flex: 1;
    padding: var(--vendor-space-8);
    max-width: 1600px;
    margin: 0 auto;
    width: 100%;
}

@media (max-width: 1024px) {
    .page-content {
        padding: var(--vendor-space-6) var(--vendor-space-4);
    }
}

@media (max-width: 768px) {
    .page-content {
        padding: var(--vendor-space-4);
    }
}

/* === MODERN BUTTONS === */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--vendor-space-2);
    padding: var(--vendor-space-3) var(--vendor-space-6);
    border: 1px solid transparent;
    border-radius: var(--vendor-radius-lg);
    font-size: var(--vendor-text-sm);
    font-weight: var(--vendor-font-medium);
    text-decoration: none;
    cursor: pointer;
    transition: var(--vendor-transition-fast);
    line-height: 1;
    white-space: nowrap;
    user-select: none;
    position: relative;
    overflow: hidden;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
}

.btn-primary {
    background: linear-gradient(135deg, var(--vendor-brand-primary), var(--vendor-brand-primary-700));
    color: var(--vendor-white);
    border-color: var(--vendor-brand-primary);
    box-shadow: var(--vendor-shadow-brand);
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--vendor-brand-primary-700), var(--vendor-brand-primary-800));
    transform: translateY(-1px);
    box-shadow: var(--vendor-shadow-brand-lg);
}

.btn-secondary {
    background: var(--vendor-surface-primary);
    color: var(--vendor-text-primary);
    border-color: var(--vendor-border-primary);
}

.btn-secondary:hover {
    background: var(--vendor-surface-hover);
    border-color: var(--vendor-brand-primary);
    color: var(--vendor-brand-primary);
}

.btn-outline {
    background: transparent;
    color: var(--vendor-brand-primary);
    border-color: var(--vendor-brand-primary);
}

.btn-outline:hover {
    background: var(--vendor-brand-primary);
    color: var(--vendor-white);
}

.btn-ghost {
    background: transparent;
    color: var(--vendor-text-secondary);
    border-color: transparent;
}

.btn-ghost:hover {
    background: var(--vendor-surface-hover);
    color: var(--vendor-text-primary);
}

.btn-success {
    background: var(--vendor-success);
    color: var(--vendor-white);
    border-color: var(--vendor-success);
}

.btn-success:hover {
    background: var(--vendor-success-dark);
    transform: translateY(-1px);
}

.btn-warning {
    background: var(--vendor-warning);
    color: var(--vendor-white);
    border-color: var(--vendor-warning);
}

.btn-warning:hover {
    background: var(--vendor-warning-dark);
    transform: translateY(-1px);
}

.btn-error {
    background: var(--vendor-error);
    color: var(--vendor-white);
    border-color: var(--vendor-error);
}

.btn-error:hover {
    background: var(--vendor-error-dark);
    transform: translateY(-1px);
}

/* Button Sizes */
.btn-xs {
    padding: var(--vendor-space-1-5) var(--vendor-space-3);
    font-size: var(--vendor-text-xs);
}

.btn-sm {
    padding: var(--vendor-space-2) var(--vendor-space-4);
    font-size: var(--vendor-text-sm);
}

.btn-lg {
    padding: var(--vendor-space-4) var(--vendor-space-8);
    font-size: var(--vendor-text-lg);
}

.btn-xl {
    padding: var(--vendor-space-5) var(--vendor-space-10);
    font-size: var(--vendor-text-xl);
}

/* === MODERN CARDS === */
.card {
    background: var(--vendor-surface-primary);
    border: 1px solid var(--vendor-border-primary);
    border-radius: var(--vendor-radius-xl);
    box-shadow: var(--vendor-shadow-sm);
    transition: var(--vendor-transition-fast);
    overflow: hidden;
}

.card:hover {
    box-shadow: var(--vendor-shadow-md);
    transform: translateY(-1px);
}

.card-header {
    padding: var(--vendor-space-6);
    border-bottom: 1px solid var(--vendor-border-primary);
    background: var(--vendor-surface-secondary);
}

.card-body {
    padding: var(--vendor-space-6);
}

.card-footer {
    padding: var(--vendor-space-6);
    border-top: 1px solid var(--vendor-border-primary);
    background: var(--vendor-surface-secondary);
}

.card-title {
    font-size: var(--vendor-text-lg);
    font-weight: var(--vendor-font-semibold);
    color: var(--vendor-text-primary);
    margin: 0 0 var(--vendor-space-2);
}

.card-subtitle {
    font-size: var(--vendor-text-sm);
    color: var(--vendor-text-secondary);
    margin: 0;
}

.card-text {
    color: var(--vendor-text-secondary);
    line-height: var(--vendor-leading-relaxed);
}

/* Card Variants */
.card-elevated {
    box-shadow: var(--vendor-shadow-lg);
    border: none;
}

.card-brand {
    border-color: var(--vendor-brand-primary);
    background: linear-gradient(135deg, var(--vendor-brand-primary-50), var(--vendor-white));
}

.card-success {
    border-color: var(--vendor-success);
    background: linear-gradient(135deg, var(--vendor-success-light), var(--vendor-white));
}

.card-warning {
    border-color: var(--vendor-warning);
    background: linear-gradient(135deg, var(--vendor-warning-light), var(--vendor-white));
}

.card-error {
    border-color: var(--vendor-error);
    background: linear-gradient(135deg, var(--vendor-error-light), var(--vendor-white));
}

/* === MODERN FORMS === */
.form-group {
    margin-bottom: var(--vendor-space-6);
}

.form-label {
    display: block;
    font-size: var(--vendor-text-sm);
    font-weight: var(--vendor-font-medium);
    color: var(--vendor-text-primary);
    margin-bottom: var(--vendor-space-2);
}

.form-label.required::after {
    content: '*';
    color: var(--vendor-error);
    margin-left: var(--vendor-space-1);
}

.form-input,
.form-select,
.form-textarea {
    width: 100%;
    padding: var(--vendor-space-3) var(--vendor-space-4);
    border: 1px solid var(--vendor-border-primary);
    border-radius: var(--vendor-radius-lg);
    font-size: var(--vendor-text-sm);
    color: var(--vendor-text-primary);
    background: var(--vendor-surface-primary);
    transition: var(--vendor-transition-fast);
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
    outline: none;
    border-color: var(--vendor-brand-primary);
    box-shadow: 0 0 0 3px rgba(126, 217, 87, 0.1);
}

.form-input::placeholder,
.form-textarea::placeholder {
    color: var(--vendor-text-tertiary);
}

.form-textarea {
    resize: vertical;
    min-height: 100px;
}

.form-help {
    font-size: var(--vendor-text-xs);
    color: var(--vendor-text-tertiary);
    margin-top: var(--vendor-space-1);
}

.form-error {
    font-size: var(--vendor-text-xs);
    color: var(--vendor-error);
    margin-top: var(--vendor-space-1);
}

.form-input.error,
.form-select.error,
.form-textarea.error {
    border-color: var(--vendor-error);
}

.form-input.success,
.form-select.success,
.form-textarea.success {
    border-color: var(--vendor-success);
}

/* === RESPONSIVE UTILITIES === */
@media (max-width: 640px) {
    .header-content {
        padding: 0 var(--vendor-space-4);
    }

    .page-title {
        font-size: var(--vendor-text-xl);
    }

    .quick-actions {
        gap: var(--vendor-space-1);
    }

    .quick-action-btn {
        width: 36px;
        height: 36px;
    }

    .user-profile-dropdown {
        width: 260px;
        right: -20px;
    }

    .btn {
        padding: var(--vendor-space-2-5) var(--vendor-space-4);
        font-size: var(--vendor-text-xs);
    }

    .card-header,
    .card-body,
    .card-footer {
        padding: var(--vendor-space-4);
    }
}

/* === MODERN BADGES === */
.badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: var(--vendor-space-1) var(--vendor-space-2-5);
    font-size: var(--vendor-text-xs);
    font-weight: var(--vendor-font-semibold);
    border-radius: var(--vendor-radius-full);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    line-height: 1;
}

.badge-primary {
    background: var(--vendor-brand-primary);
    color: var(--vendor-white);
}

.badge-secondary {
    background: var(--vendor-surface-hover);
    color: var(--vendor-text-secondary);
}

.badge-success {
    background: var(--vendor-success);
    color: var(--vendor-white);
}

.badge-warning {
    background: var(--vendor-warning);
    color: var(--vendor-white);
}

.badge-error {
    background: var(--vendor-error);
    color: var(--vendor-white);
}

.badge-info {
    background: var(--vendor-info);
    color: var(--vendor-white);
}

/* Badge Variants */
.badge-outline {
    background: transparent;
    border: 1px solid currentColor;
}

.badge-soft {
    background: var(--vendor-brand-primary-100);
    color: var(--vendor-brand-primary-700);
}

.badge-soft.badge-success {
    background: var(--vendor-success-light);
    color: var(--vendor-success-dark);
}

.badge-soft.badge-warning {
    background: var(--vendor-warning-light);
    color: var(--vendor-warning-dark);
}

.badge-soft.badge-error {
    background: var(--vendor-error-light);
    color: var(--vendor-error-dark);
}

.badge-soft.badge-info {
    background: var(--vendor-info-light);
    color: var(--vendor-info-dark);
}

/* === MODERN ALERTS === */
.alert {
    padding: var(--vendor-space-4) var(--vendor-space-6);
    border-radius: var(--vendor-radius-lg);
    border: 1px solid;
    display: flex;
    align-items: flex-start;
    gap: var(--vendor-space-3);
    margin-bottom: var(--vendor-space-4);
}

.alert-icon {
    flex-shrink: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: var(--vendor-space-0-5);
}

.alert-content {
    flex: 1;
}

.alert-title {
    font-size: var(--vendor-text-sm);
    font-weight: var(--vendor-font-semibold);
    margin: 0 0 var(--vendor-space-1);
}

.alert-message {
    font-size: var(--vendor-text-sm);
    margin: 0;
    line-height: var(--vendor-leading-relaxed);
}

.alert-close {
    flex-shrink: 0;
    width: 20px;
    height: 20px;
    border: none;
    background: transparent;
    cursor: pointer;
    border-radius: var(--vendor-radius-base);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--vendor-transition-fast);
}

.alert-close:hover {
    background: rgba(0, 0, 0, 0.1);
}

/* Alert Variants */
.alert-success {
    background: var(--vendor-success-light);
    border-color: var(--vendor-success);
    color: var(--vendor-success-dark);
}

.alert-warning {
    background: var(--vendor-warning-light);
    border-color: var(--vendor-warning);
    color: var(--vendor-warning-dark);
}

.alert-error {
    background: var(--vendor-error-light);
    border-color: var(--vendor-error);
    color: var(--vendor-error-dark);
}

.alert-info {
    background: var(--vendor-info-light);
    border-color: var(--vendor-info);
    color: var(--vendor-info-dark);
}

/* === MODERN STATS CARDS === */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--vendor-space-6);
    margin-bottom: var(--vendor-space-8);
}

.stat-card {
    background: var(--vendor-surface-primary);
    border: 1px solid var(--vendor-border-primary);
    border-radius: var(--vendor-radius-xl);
    padding: var(--vendor-space-6);
    position: relative;
    overflow: hidden;
    transition: var(--vendor-transition-fast);
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--vendor-shadow-lg);
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--vendor-brand-primary), var(--vendor-brand-primary-700));
}

.stat-card.success::before {
    background: linear-gradient(90deg, var(--vendor-success), var(--vendor-success-dark));
}

.stat-card.warning::before {
    background: linear-gradient(90deg, var(--vendor-warning), var(--vendor-warning-dark));
}

.stat-card.error::before {
    background: linear-gradient(90deg, var(--vendor-error), var(--vendor-error-dark));
}

.stat-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--vendor-space-4);
}

.stat-icon {
    width: 48px;
    height: 48px;
    border-radius: var(--vendor-radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--vendor-brand-primary-100), var(--vendor-brand-primary-50));
    color: var(--vendor-brand-primary);
    font-size: var(--vendor-text-xl);
}

.stat-card.success .stat-icon {
    background: linear-gradient(135deg, var(--vendor-success-light), rgba(16, 185, 129, 0.1));
    color: var(--vendor-success);
}

.stat-card.warning .stat-icon {
    background: linear-gradient(135deg, var(--vendor-warning-light), rgba(245, 158, 11, 0.1));
    color: var(--vendor-warning);
}

.stat-card.error .stat-icon {
    background: linear-gradient(135deg, var(--vendor-error-light), rgba(239, 68, 68, 0.1));
    color: var(--vendor-error);
}

.stat-menu {
    width: 32px;
    height: 32px;
    border: none;
    background: transparent;
    border-radius: var(--vendor-radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: var(--vendor-text-tertiary);
    transition: var(--vendor-transition-fast);
}

.stat-menu:hover {
    background: var(--vendor-surface-hover);
    color: var(--vendor-text-secondary);
}

.stat-content {
    margin-bottom: var(--vendor-space-4);
}

.stat-label {
    font-size: var(--vendor-text-sm);
    color: var(--vendor-text-secondary);
    margin-bottom: var(--vendor-space-2);
    font-weight: var(--vendor-font-medium);
}

.stat-value {
    font-size: var(--vendor-text-3xl);
    font-weight: var(--vendor-font-bold);
    color: var(--vendor-text-primary);
    margin-bottom: var(--vendor-space-2);
    line-height: var(--vendor-leading-tight);
}

.stat-change {
    display: flex;
    align-items: center;
    gap: var(--vendor-space-1);
    font-size: var(--vendor-text-sm);
    font-weight: var(--vendor-font-medium);
}

.stat-change.positive {
    color: var(--vendor-success);
}

.stat-change.negative {
    color: var(--vendor-error);
}

.stat-change.neutral {
    color: var(--vendor-text-tertiary);
}

.stat-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-top: var(--vendor-space-4);
    border-top: 1px solid var(--vendor-border-primary);
}

.stat-period {
    font-size: var(--vendor-text-xs);
    color: var(--vendor-text-tertiary);
}

.stat-link {
    font-size: var(--vendor-text-xs);
    color: var(--vendor-brand-primary);
    text-decoration: none;
    font-weight: var(--vendor-font-medium);
    transition: var(--vendor-transition-fast);
}

.stat-link:hover {
    color: var(--vendor-brand-primary-700);
}

@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: 1fr;
        gap: var(--vendor-space-4);
    }

    .stat-card {
        padding: var(--vendor-space-4);
    }

    .stat-value {
        font-size: var(--vendor-text-2xl);
    }
}
