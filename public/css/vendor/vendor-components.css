/* ===== VENDOR COMPONENTS CSS ===== */

/* === HEADER COMPONENT === */
.vendor-header {
    background: #ffffff;
    border-bottom: 1px solid #e5e7eb;
    height: 80px;
    position: sticky;
    top: 0;
    z-index: 1000;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    backdrop-filter: blur(10px);
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 32px;
    height: 100%;
}

@media (max-width: 1024px) {
    .vendor-header {
        height: 64px;
    }

    .header-content {
        padding: 0 16px;
    }
}

.header-left {
    display: flex;
    align-items: center;
    gap: 16px;
}

.mobile-menu-btn {
    display: none;
    background: none;
    border: none;
    color: var(--vendor-text-secondary);
    font-size: var(--vendor-text-xl);
    cursor: pointer;
    padding: var(--vendor-space-2);
    border-radius: var(--vendor-radius-base);
    transition: var(--vendor-transition-fast);
}

.mobile-menu-btn:hover {
    background: var(--vendor-surface-hover);
    color: var(--vendor-text-primary);
}

.mobile-only {
    display: none;
}

@media (max-width: 1024px) {
    .mobile-menu-btn {
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .mobile-only {
        display: flex;
    }

    .desktop-only {
        display: none;
    }
}

.header-title {
    font-size: var(--vendor-text-2xl);
    font-weight: var(--vendor-font-bold);
    color: var(--vendor-text-primary);
    margin: 0;
}

.header-breadcrumb {
    display: flex;
    align-items: center;
    gap: var(--vendor-space-2);
    font-size: var(--vendor-text-sm);
    color: var(--vendor-text-tertiary);
}

.breadcrumb-item {
    color: var(--vendor-text-tertiary);
    text-decoration: none;
    transition: var(--vendor-transition-fast);
}

.breadcrumb-item:hover {
    color: var(--vendor-text-primary);
}

.breadcrumb-item.active {
    color: var(--vendor-text-primary);
    font-weight: var(--vendor-font-medium);
}

.breadcrumb-separator {
    color: var(--vendor-text-tertiary);
    font-size: var(--vendor-text-xs);
}

/* Header Center - Search */
.header-center {
    flex: 1;
    max-width: 600px;
    margin: 0 var(--vendor-space-8);
}

@media (max-width: 768px) {
    .header-center {
        display: none;
    }
}

.search-container {
    position: relative;
    width: 100%;
}

.search-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    background: var(--vendor-surface-secondary);
    border: 2px solid transparent;
    border-radius: var(--vendor-radius-lg);
    transition: var(--vendor-transition-fast);
    overflow: hidden;
}

.search-input-wrapper.focused {
    background: var(--vendor-surface-primary);
    border-color: var(--vendor-brand-primary);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.search-icon {
    position: absolute;
    left: var(--vendor-space-4);
    color: var(--vendor-text-tertiary);
    font-size: var(--vendor-text-lg);
    z-index: 1;
}

.search-input {
    width: 100%;
    padding: var(--vendor-space-3) var(--vendor-space-12) var(--vendor-space-3) var(--vendor-space-12);
    border: none;
    background: transparent;
    color: var(--vendor-text-primary);
    font-size: var(--vendor-text-base);
    outline: none;
    transition: var(--vendor-transition-fast);
}

.search-input::placeholder {
    color: var(--vendor-text-tertiary);
}

.search-shortcut {
    position: absolute;
    right: var(--vendor-space-4);
    background: var(--vendor-surface-hover);
    color: var(--vendor-text-tertiary);
    font-size: var(--vendor-text-xs);
    padding: 2px 6px;
    border-radius: var(--vendor-radius-sm);
    font-weight: var(--vendor-font-medium);
}

/* Header Right */
.header-right {
    display: flex;
    align-items: center;
    gap: var(--vendor-space-3);
}

.header-action {
    position: relative;
    background: none;
    border: none;
    color: var(--vendor-text-secondary);
    font-size: var(--vendor-text-lg);
    cursor: pointer;
    padding: var(--vendor-space-2);
    border-radius: var(--vendor-radius-base);
    transition: var(--vendor-transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
}

.header-action:hover {
    background: var(--vendor-surface-hover);
    color: var(--vendor-text-primary);
}

.header-action-badge {
    position: absolute;
    top: 0;
    right: 0;
    background: var(--vendor-error);
    color: var(--vendor-white);
    font-size: 10px;
    font-weight: var(--vendor-font-bold);
    padding: 2px 5px;
    border-radius: var(--vendor-radius-full);
    min-width: 16px;
    text-align: center;
    transform: translate(25%, -25%);
}

/* Theme Toggle */
.theme-toggle {
    background: var(--vendor-surface-secondary);
    border: 1px solid var(--vendor-border-primary);
    color: var(--vendor-text-secondary);
    font-size: var(--vendor-text-base);
    cursor: pointer;
    padding: var(--vendor-space-2);
    border-radius: var(--vendor-radius-base);
    transition: var(--vendor-transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
}

.theme-toggle:hover {
    background: var(--vendor-surface-hover);
    color: var(--vendor-text-primary);
    border-color: var(--vendor-border-secondary);
}

/* Profile Dropdown */
.profile-dropdown {
    position: relative;
}

.profile-trigger {
    display: flex;
    align-items: center;
    gap: var(--vendor-space-3);
    background: none;
    border: none;
    cursor: pointer;
    padding: var(--vendor-space-2);
    border-radius: var(--vendor-radius-md);
    transition: var(--vendor-transition-fast);
}

.profile-trigger:hover {
    background: var(--vendor-surface-hover);
}

.profile-avatar {
    width: 36px;
    height: 36px;
    border-radius: var(--vendor-radius-full);
    background: var(--vendor-brand-primary);
    color: var(--vendor-white);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: var(--vendor-font-semibold);
    font-size: var(--vendor-text-sm);
}

.profile-info {
    text-align: left;
}

@media (max-width: 640px) {
    .profile-info {
        display: none;
    }
}

.profile-name {
    font-size: var(--vendor-text-sm);
    font-weight: var(--vendor-font-semibold);
    color: var(--vendor-text-primary);
    line-height: 1.2;
}

.profile-role {
    font-size: var(--vendor-text-xs);
    color: var(--vendor-text-tertiary);
    line-height: 1.2;
}

.profile-chevron {
    color: var(--vendor-text-tertiary);
    font-size: var(--vendor-text-sm);
    transition: var(--vendor-transition-fast);
}

.profile-dropdown.open .profile-chevron {
    transform: rotate(180deg);
}

.profile-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: var(--vendor-surface-primary);
    border: 1px solid var(--vendor-border-primary);
    border-radius: var(--vendor-radius-lg);
    box-shadow: var(--vendor-shadow-lg);
    min-width: 220px;
    padding: var(--vendor-space-2);
    z-index: var(--vendor-z-dropdown);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: var(--vendor-transition-fast);
}

.profile-dropdown.open .profile-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.profile-menu-item {
    display: flex;
    align-items: center;
    gap: var(--vendor-space-3);
    width: 100%;
    padding: var(--vendor-space-3);
    border: none;
    background: none;
    color: var(--vendor-text-secondary);
    text-decoration: none;
    border-radius: var(--vendor-radius-md);
    transition: var(--vendor-transition-fast);
    cursor: pointer;
    font-size: var(--vendor-text-sm);
}

.profile-menu-item:hover {
    background: var(--vendor-surface-hover);
    color: var(--vendor-text-primary);
}

.profile-menu-item.logout-item {
    color: var(--vendor-error);
}

.profile-menu-item.logout-item:hover {
    background: var(--vendor-error-50);
    color: var(--vendor-error-700);
}

.profile-menu-divider {
    height: 1px;
    background: var(--vendor-border-primary);
    margin: var(--vendor-space-2) 0;
}

/* === MAIN CONTENT === */
.vendor-main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    margin-left: 0;
    transition: var(--vendor-transition-base);
}

@media (min-width: 1025px) {
    .vendor-main-content {
        margin-left: var(--vendor-sidebar-width);
    }

    .vendor-sidebar.collapsed + .vendor-main-content {
        margin-left: var(--vendor-sidebar-collapsed-width);
    }
}

.page-header-actions {
    background: var(--vendor-surface-primary);
    border-bottom: 1px solid var(--vendor-border-primary);
    padding: var(--vendor-space-4) var(--vendor-space-8);
}

@media (max-width: 1024px) {
    .page-header-actions {
        padding: var(--vendor-space-4);
    }
}

.page-content {
    flex: 1;
    padding: var(--vendor-space-8);
    max-width: var(--vendor-content-max-width);
    margin: 0 auto;
    width: 100%;
}

@media (max-width: 1024px) {
    .page-content {
        padding: var(--vendor-space-4);
    }
}

/* === USER PROFILE & NOTIFICATIONS === */
.header-right {
    display: flex;
    align-items: center;
    gap: 12px;
}

.notifications-container,
.user-profile-container {
    position: relative;
}

.notifications-btn,
.user-profile-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 8px;
    border-radius: 8px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.notifications-btn:hover,
.user-profile-btn:hover {
    background: #f3f4f6;
}

.notifications-icon {
    position: relative;
    color: #6b7280;
    font-size: 18px;
}

.notifications-badge {
    position: absolute;
    top: -4px;
    right: -4px;
    background: #ef4444;
    color: #ffffff;
    font-size: 10px;
    font-weight: 700;
    padding: 2px 5px;
    border-radius: 50px;
    min-width: 16px;
    text-align: center;
}

.user-avatar-wrapper {
    position: relative;
}

.user-avatar-img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: 2px solid #e5e7eb;
}

.user-status-dot {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 10px;
    height: 10px;
    background: var(--vendor-success);
    border: 2px solid var(--vendor-white);
    border-radius: 50%;
}

.user-info {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.user-name {
    font-size: var(--vendor-text-sm);
    font-weight: var(--vendor-font-semibold);
    color: var(--vendor-text-primary);
    line-height: 1.2;
}

.user-role {
    font-size: var(--vendor-text-xs);
    color: var(--vendor-text-tertiary);
    line-height: 1.2;
}

.user-dropdown-arrow {
    color: var(--vendor-text-tertiary);
    font-size: var(--vendor-text-sm);
    transition: var(--vendor-transition-fast);
}

.rotate-180 {
    transform: rotate(180deg);
}

/* === CARD COMPONENT === */
.card {
    background: var(--vendor-surface-primary);
    border: 1px solid var(--vendor-border-primary);
    border-radius: var(--vendor-radius-lg);
    box-shadow: var(--vendor-shadow-sm);
    overflow: hidden;
    transition: var(--vendor-transition-fast);
}

.card:hover {
    box-shadow: var(--vendor-shadow-md);
}

.card-header {
    padding: var(--vendor-space-6);
    border-bottom: 1px solid var(--vendor-border-primary);
    background: var(--vendor-surface-secondary);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.card-title {
    font-size: var(--vendor-text-lg);
    font-weight: var(--vendor-font-semibold);
    color: var(--vendor-text-primary);
    margin: 0;
}

.card-body {
    padding: var(--vendor-space-6);
}

.card-footer {
    padding: var(--vendor-space-4) var(--vendor-space-6);
    border-top: 1px solid var(--vendor-border-primary);
    background: var(--vendor-surface-secondary);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

/* === BUTTON COMPONENT === */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--vendor-space-2);
    padding: var(--vendor-space-3) var(--vendor-space-6);
    border: 1px solid transparent;
    border-radius: var(--vendor-radius-md);
    font-size: var(--vendor-text-sm);
    font-weight: var(--vendor-font-medium);
    text-decoration: none;
    cursor: pointer;
    transition: var(--vendor-transition-fast);
    line-height: 1;
    white-space: nowrap;
    user-select: none;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.btn-primary {
    background: var(--vendor-brand-primary);
    color: var(--vendor-white);
    border-color: var(--vendor-brand-primary);
}

.btn-primary:hover:not(:disabled) {
    background: var(--vendor-brand-primary-600);
    border-color: var(--vendor-brand-primary-600);
    transform: translateY(-1px);
    box-shadow: var(--vendor-shadow-md);
}

.btn-secondary {
    background: var(--vendor-surface-secondary);
    color: var(--vendor-text-primary);
    border-color: var(--vendor-border-primary);
}

.btn-secondary:hover:not(:disabled) {
    background: var(--vendor-surface-hover);
    border-color: var(--vendor-border-secondary);
}

.btn-success {
    background: var(--vendor-success);
    color: var(--vendor-white);
    border-color: var(--vendor-success);
}

.btn-success:hover:not(:disabled) {
    background: var(--vendor-success-600);
    border-color: var(--vendor-success-600);
    transform: translateY(-1px);
    box-shadow: var(--vendor-shadow-md);
}

.btn-warning {
    background: var(--vendor-warning);
    color: var(--vendor-white);
    border-color: var(--vendor-warning);
}

.btn-warning:hover:not(:disabled) {
    background: var(--vendor-warning-600);
    border-color: var(--vendor-warning-600);
    transform: translateY(-1px);
    box-shadow: var(--vendor-shadow-md);
}

.btn-error {
    background: var(--vendor-error);
    color: var(--vendor-white);
    border-color: var(--vendor-error);
}

.btn-error:hover:not(:disabled) {
    background: var(--vendor-error-600);
    border-color: var(--vendor-error-600);
    transform: translateY(-1px);
    box-shadow: var(--vendor-shadow-md);
}

.btn-ghost {
    background: transparent;
    color: var(--vendor-text-secondary);
    border-color: transparent;
}

.btn-ghost:hover:not(:disabled) {
    background: var(--vendor-surface-hover);
    color: var(--vendor-text-primary);
}

/* Button Sizes */
.btn-sm {
    padding: var(--vendor-space-2) var(--vendor-space-4);
    font-size: var(--vendor-text-xs);
}

.btn-lg {
    padding: var(--vendor-space-4) var(--vendor-space-8);
    font-size: var(--vendor-text-base);
}

.btn-xl {
    padding: var(--vendor-space-5) var(--vendor-space-10);
    font-size: var(--vendor-text-lg);
}
