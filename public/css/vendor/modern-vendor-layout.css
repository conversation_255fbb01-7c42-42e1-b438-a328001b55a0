/* ===== MODERN VENDOR LAYOUT CSS ===== */

/* === LOADING SCREEN === */
.modern-loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, var(--vendor-brand-primary), var(--vendor-brand-primary-700));
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: var(--vendor-z-modal);
    backdrop-filter: blur(10px);
}

.loading-content {
    text-align: center;
    color: var(--vendor-white);
    animation: fadeInUp 0.6s ease-out;
}

.loading-logo {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--vendor-space-4);
    margin-bottom: var(--vendor-space-6);
}

.logo-container {
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--vendor-radius-2xl);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.logo-image {
    width: 50px;
    height: 50px;
    object-fit: contain;
}

.logo-fallback {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px;
    font-size: var(--vendor-text-2xl);
    color: var(--vendor-white);
}

.loading-spinner {
    display: flex;
    justify-content: center;
    gap: var(--vendor-space-1);
}

.spinner-ring {
    width: 8px;
    height: 8px;
    border-radius: var(--vendor-radius-full);
    background: var(--vendor-white);
    animation: spinnerPulse 1.4s ease-in-out infinite both;
}

.spinner-ring:nth-child(1) { animation-delay: -0.32s; }
.spinner-ring:nth-child(2) { animation-delay: -0.16s; }

.loading-text h3 {
    font-size: var(--vendor-text-xl);
    font-weight: var(--vendor-font-semibold);
    margin-bottom: var(--vendor-space-2);
    color: var(--vendor-white);
}

.loading-text p {
    font-size: var(--vendor-text-sm);
    opacity: 0.8;
    color: var(--vendor-white);
}

/* === MOBILE SIDEBAR OVERLAY === */
.mobile-sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: var(--vendor-z-modal-backdrop);
    backdrop-filter: blur(4px);
}

/* === MAIN APP CONTAINER === */
.modern-vendor-app {
    display: flex;
    min-height: 100vh;
    background: var(--vendor-bg-primary);
    position: relative;
}

/* === MODERN SIDEBAR === */
.modern-sidebar {
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    width: 280px;
    background: var(--vendor-surface-primary);
    border-right: 1px solid var(--vendor-border-primary);
    z-index: var(--vendor-z-fixed);
    display: flex;
    flex-direction: column;
    transition: var(--vendor-transition-base);
    box-shadow: var(--vendor-shadow-lg);
}

.modern-sidebar.collapsed {
    width: 80px;
}

.modern-sidebar.mobile-open {
    transform: translateX(0);
}

@media (max-width: 1024px) {
    .modern-sidebar {
        transform: translateX(-100%);
    }
}

/* === SIDEBAR HEADER === */
.sidebar-header {
    padding: var(--vendor-space-6);
    border-bottom: 1px solid var(--vendor-border-primary);
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-height: 80px;
}

.brand-container {
    display: flex;
    align-items: center;
    gap: var(--vendor-space-3);
    flex: 1;
}

.brand-container.collapsed {
    justify-content: center;
}

.brand-logo {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--vendor-brand-primary), var(--vendor-brand-primary-700));
    border-radius: var(--vendor-radius-lg);
    flex-shrink: 0;
}

.logo-full {
    width: 28px;
    height: 28px;
    object-fit: contain;
}

.logo-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 28px;
    height: 28px;
    color: var(--vendor-white);
    font-size: var(--vendor-text-lg);
}

.brand-text h2 {
    font-size: var(--vendor-text-xl);
    font-weight: var(--vendor-font-bold);
    color: var(--vendor-text-primary);
    margin: 0;
}

.brand-text span {
    font-size: var(--vendor-text-xs);
    color: var(--vendor-text-tertiary);
    font-weight: var(--vendor-font-medium);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.sidebar-toggle-btn {
    width: 32px;
    height: 32px;
    border: none;
    background: var(--vendor-surface-hover);
    border-radius: var(--vendor-radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--vendor-transition-fast);
    color: var(--vendor-text-secondary);
}

.sidebar-toggle-btn:hover {
    background: var(--vendor-brand-primary-100);
    color: var(--vendor-brand-primary);
}

.sidebar-toggle-btn i {
    transition: var(--vendor-transition-fast);
}

.sidebar-toggle-btn i.rotate-180 {
    transform: rotate(180deg);
}

/* === SIDEBAR NAVIGATION === */
.sidebar-nav {
    flex: 1;
    padding: var(--vendor-space-4) 0;
    overflow-y: auto;
    overflow-x: hidden;
}

.nav-section {
    margin-bottom: var(--vendor-space-6);
}

.nav-section:last-child {
    margin-bottom: 0;
}

.nav-section-title {
    padding: 0 var(--vendor-space-6) var(--vendor-space-3);
    font-size: var(--vendor-text-xs);
    font-weight: var(--vendor-font-semibold);
    color: var(--vendor-text-tertiary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.nav-menu {
    list-style: none;
    margin: 0;
    padding: 0;
}

.nav-item {
    margin: 0;
    padding: 0 var(--vendor-space-4);
}

.nav-link {
    display: flex;
    align-items: center;
    gap: var(--vendor-space-3);
    padding: var(--vendor-space-3) var(--vendor-space-4);
    border-radius: var(--vendor-radius-lg);
    color: var(--vendor-text-secondary);
    text-decoration: none;
    transition: var(--vendor-transition-fast);
    position: relative;
    margin-bottom: var(--vendor-space-1);
    font-weight: var(--vendor-font-medium);
    font-size: var(--vendor-text-sm);
}

.nav-link:hover {
    background: var(--vendor-surface-hover);
    color: var(--vendor-text-primary);
}

.nav-link.active {
    background: linear-gradient(135deg, var(--vendor-brand-primary-100), var(--vendor-brand-primary-50));
    color: var(--vendor-brand-primary-700);
    font-weight: var(--vendor-font-semibold);
}

.nav-icon {
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    font-size: var(--vendor-text-base);
}

.nav-text {
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.nav-badge {
    background: var(--vendor-brand-primary);
    color: var(--vendor-white);
    font-size: var(--vendor-text-xs);
    font-weight: var(--vendor-font-semibold);
    padding: var(--vendor-space-1) var(--vendor-space-2);
    border-radius: var(--vendor-radius-full);
    min-width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.nav-indicator {
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 3px;
    height: 0;
    background: var(--vendor-brand-primary);
    border-radius: 0 var(--vendor-radius-full) var(--vendor-radius-full) 0;
    transition: var(--vendor-transition-fast);
}

.nav-link.active .nav-indicator {
    height: 20px;
}

/* === SIDEBAR FOOTER === */
.sidebar-footer {
    padding: var(--vendor-space-4) var(--vendor-space-6) var(--vendor-space-6);
    border-top: 1px solid var(--vendor-border-primary);
}

.upgrade-card {
    background: linear-gradient(135deg, var(--vendor-brand-primary-50), var(--vendor-brand-primary-100));
    border: 1px solid var(--vendor-brand-primary-200);
    border-radius: var(--vendor-radius-xl);
    padding: var(--vendor-space-4);
    text-align: center;
}

.upgrade-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--vendor-brand-primary), var(--vendor-brand-primary-700));
    border-radius: var(--vendor-radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--vendor-space-3);
    color: var(--vendor-white);
    font-size: var(--vendor-text-lg);
}

.upgrade-content h4 {
    font-size: var(--vendor-text-sm);
    font-weight: var(--vendor-font-semibold);
    color: var(--vendor-text-primary);
    margin-bottom: var(--vendor-space-1);
}

.upgrade-content p {
    font-size: var(--vendor-text-xs);
    color: var(--vendor-text-tertiary);
    margin-bottom: var(--vendor-space-3);
}

.upgrade-btn {
    background: var(--vendor-brand-primary);
    color: var(--vendor-white);
    border: none;
    border-radius: var(--vendor-radius-md);
    padding: var(--vendor-space-2) var(--vendor-space-3);
    font-size: var(--vendor-text-xs);
    font-weight: var(--vendor-font-medium);
    cursor: pointer;
    transition: var(--vendor-transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--vendor-space-1);
    width: 100%;
}

.upgrade-btn:hover {
    background: var(--vendor-brand-primary-700);
    transform: translateY(-1px);
}

/* === MAIN CONTENT WRAPPER === */
.modern-main-wrapper {
    flex: 1;
    display: flex;
    flex-direction: column;
    margin-left: 280px;
    transition: var(--vendor-transition-base);
    min-height: 100vh;
    background: var(--vendor-bg-primary);
}

.modern-vendor-body.sidebar-collapsed .modern-main-wrapper {
    margin-left: 80px;
}

@media (max-width: 1024px) {
    .modern-main-wrapper {
        margin-left: 0;
    }
}

/* === ANIMATIONS === */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes spinnerPulse {
    0%, 80%, 100% {
        transform: scale(0);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

/* === ADVANCED RESPONSIVE LAYOUT === */

/* Tablet Layout */
@media (max-width: 1024px) and (min-width: 769px) {
    .modern-sidebar {
        width: 80px;
    }

    .modern-sidebar .nav-text,
    .modern-sidebar .nav-badge,
    .modern-sidebar .nav-section-title,
    .modern-sidebar .brand-text,
    .modern-sidebar .upgrade-card {
        display: none;
    }

    .modern-sidebar .brand-container {
        justify-content: center;
    }

    .modern-sidebar .sidebar-toggle-btn {
        display: none;
    }

    .modern-main-wrapper {
        margin-left: 80px;
    }
}

/* Mobile Layout */
@media (max-width: 768px) {
    .modern-sidebar {
        width: 280px;
        transform: translateX(-100%);
    }

    .modern-sidebar.mobile-open {
        transform: translateX(0);
    }

    .modern-main-wrapper {
        margin-left: 0;
    }

    .page-content {
        padding: var(--vendor-space-4);
    }

    .header-content {
        padding: 0 var(--vendor-space-4);
    }
}

/* Small Mobile Layout */
@media (max-width: 480px) {
    .modern-sidebar {
        width: 100vw;
    }

    .page-content {
        padding: var(--vendor-space-3);
    }

    .header-content {
        padding: 0 var(--vendor-space-3);
    }

    .sidebar-header {
        padding: var(--vendor-space-4);
    }

    .sidebar-nav {
        padding: var(--vendor-space-3) 0;
    }

    .nav-item {
        padding: 0 var(--vendor-space-3);
    }

    .sidebar-footer {
        padding: var(--vendor-space-3) var(--vendor-space-4) var(--vendor-space-4);
    }
}

/* === PRINT STYLES === */
@media print {
    .modern-sidebar,
    .mobile-sidebar-overlay,
    .modern-loading-screen {
        display: none !important;
    }

    .modern-main-wrapper {
        margin-left: 0 !important;
    }

    .modern-header {
        position: static !important;
        box-shadow: none !important;
        border-bottom: 1px solid #000 !important;
    }

    .page-content {
        padding: 0 !important;
    }

    * {
        background: white !important;
        color: black !important;
        box-shadow: none !important;
    }
}

/* === ACCESSIBILITY IMPROVEMENTS === */

/* High contrast mode */
@media (prefers-contrast: high) {
    :root {
        --vendor-border-primary: #000000;
        --vendor-text-secondary: #000000;
        --vendor-text-tertiary: #333333;
    }

    .nav-link:hover,
    .nav-link.active {
        background: #000000;
        color: #ffffff;
    }

    .btn {
        border-width: 2px;
    }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }

    .modern-sidebar {
        transition: none;
    }

    .nav-link,
    .btn,
    .quick-action-btn {
        transition: none;
    }
}

/* Focus visible improvements */
.nav-link:focus-visible,
.btn:focus-visible,
.quick-action-btn:focus-visible,
.sidebar-toggle-btn:focus-visible {
    outline: 2px solid var(--vendor-brand-primary);
    outline-offset: 2px;
    box-shadow: 0 0 0 4px rgba(126, 217, 87, 0.2);
}

/* === PERFORMANCE OPTIMIZATIONS === */

/* GPU acceleration for animations */
.modern-sidebar,
.nav-link,
.btn,
.quick-action-btn {
    will-change: transform;
    transform: translateZ(0);
}

/* Optimize scrolling */
.sidebar-nav {
    -webkit-overflow-scrolling: touch;
    scrollbar-width: thin;
    scrollbar-color: var(--vendor-border-secondary) transparent;
}

/* === DARK MODE SPECIFIC ADJUSTMENTS === */
.dark .modern-loading-screen {
    background: linear-gradient(135deg, var(--vendor-gray-900), var(--vendor-gray-800));
}

.dark .modern-sidebar {
    background: var(--vendor-gray-800);
    border-right-color: var(--vendor-gray-700);
}

.dark .sidebar-header {
    border-bottom-color: var(--vendor-gray-700);
}

.dark .sidebar-footer {
    border-top-color: var(--vendor-gray-700);
}

.dark .upgrade-card {
    background: linear-gradient(135deg, rgba(126, 217, 87, 0.1), rgba(126, 217, 87, 0.05));
    border-color: rgba(126, 217, 87, 0.2);
}

/* === CONTAINER QUERIES (Future-proofing) === */
@supports (container-type: inline-size) {
    .modern-main-wrapper {
        container-type: inline-size;
    }

    @container (max-width: 600px) {
        .page-content {
            padding: var(--vendor-space-4);
        }
    }
}
