/* ===== MODERN VENDOR BASE CSS - DESIGN SYSTEM ===== */

/* CSS Custom Properties - Design Tokens */
:root {
    /* === BRAND COLORS - WhaMart Green Theme === */
    --vendor-brand-primary: #7ED957;
    --vendor-brand-primary-50: #F8FFF5;
    --vendor-brand-primary-100: #E8FFE1;
    --vendor-brand-primary-200: #D1FFB8;
    --vendor-brand-primary-300: #B8FF8F;
    --vendor-brand-primary-400: #9FFF66;
    --vendor-brand-primary-500: #7ED957;
    --vendor-brand-primary-600: #5DC264;
    --vendor-brand-primary-700: #4CAF50;
    --vendor-brand-primary-800: #3EA045;
    --vendor-brand-primary-900: #2E7D32;

    /* === WHATSAPP GREEN COLORS === */
    --whatsapp-green: #7ED957;
    --whatsapp-green-dark: #4CAF50;
    --whatsapp-teal: #4CAF50;
    --whatsapp-teal-dark: #3EA045;

    /* === SEMANTIC COLORS === */
    --vendor-success: #10B981;
    --vendor-success-light: #D1FAE5;
    --vendor-success-dark: #047857;
    
    --vendor-warning: #F59E0B;
    --vendor-warning-light: #FEF3C7;
    --vendor-warning-dark: #D97706;
    
    --vendor-error: #EF4444;
    --vendor-error-light: #FEE2E2;
    --vendor-error-dark: #DC2626;
    
    --vendor-info: #3B82F6;
    --vendor-info-light: #DBEAFE;
    --vendor-info-dark: #1D4ED8;

    /* === NEUTRAL COLORS === */
    --vendor-white: #FFFFFF;
    --vendor-black: #000000;
    
    --vendor-gray-50: #F9FAFB;
    --vendor-gray-100: #F3F4F6;
    --vendor-gray-200: #E5E7EB;
    --vendor-gray-300: #D1D5DB;
    --vendor-gray-400: #9CA3AF;
    --vendor-gray-500: #6B7280;
    --vendor-gray-600: #4B5563;
    --vendor-gray-700: #374151;
    --vendor-gray-800: #1F2937;
    --vendor-gray-900: #111827;

    /* === TYPOGRAPHY === */
    --vendor-font-family-primary: 'Inter', 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --vendor-font-family-secondary: 'Poppins', 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --vendor-font-family-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;

    /* Font Weights */
    --vendor-font-thin: 100;
    --vendor-font-extralight: 200;
    --vendor-font-light: 300;
    --vendor-font-normal: 400;
    --vendor-font-medium: 500;
    --vendor-font-semibold: 600;
    --vendor-font-bold: 700;
    --vendor-font-extrabold: 800;
    --vendor-font-black: 900;

    /* Font Sizes */
    --vendor-text-xs: 0.75rem;      /* 12px */
    --vendor-text-sm: 0.875rem;     /* 14px */
    --vendor-text-base: 1rem;       /* 16px */
    --vendor-text-lg: 1.125rem;     /* 18px */
    --vendor-text-xl: 1.25rem;      /* 20px */
    --vendor-text-2xl: 1.5rem;      /* 24px */
    --vendor-text-3xl: 1.875rem;    /* 30px */
    --vendor-text-4xl: 2.25rem;     /* 36px */
    --vendor-text-5xl: 3rem;        /* 48px */
    --vendor-text-6xl: 3.75rem;     /* 60px */

    /* Line Heights */
    --vendor-leading-none: 1;
    --vendor-leading-tight: 1.25;
    --vendor-leading-snug: 1.375;
    --vendor-leading-normal: 1.5;
    --vendor-leading-relaxed: 1.625;
    --vendor-leading-loose: 2;

    /* === SPACING === */
    --vendor-space-0: 0;
    --vendor-space-px: 1px;
    --vendor-space-0-5: 0.125rem;   /* 2px */
    --vendor-space-1: 0.25rem;      /* 4px */
    --vendor-space-1-5: 0.375rem;   /* 6px */
    --vendor-space-2: 0.5rem;       /* 8px */
    --vendor-space-2-5: 0.625rem;   /* 10px */
    --vendor-space-3: 0.75rem;      /* 12px */
    --vendor-space-3-5: 0.875rem;   /* 14px */
    --vendor-space-4: 1rem;         /* 16px */
    --vendor-space-5: 1.25rem;      /* 20px */
    --vendor-space-6: 1.5rem;       /* 24px */
    --vendor-space-7: 1.75rem;      /* 28px */
    --vendor-space-8: 2rem;         /* 32px */
    --vendor-space-9: 2.25rem;      /* 36px */
    --vendor-space-10: 2.5rem;      /* 40px */
    --vendor-space-11: 2.75rem;     /* 44px */
    --vendor-space-12: 3rem;        /* 48px */
    --vendor-space-14: 3.5rem;      /* 56px */
    --vendor-space-16: 4rem;        /* 64px */
    --vendor-space-20: 5rem;        /* 80px */
    --vendor-space-24: 6rem;        /* 96px */
    --vendor-space-28: 7rem;        /* 112px */
    --vendor-space-32: 8rem;        /* 128px */

    /* === BORDER RADIUS === */
    --vendor-radius-none: 0;
    --vendor-radius-sm: 0.125rem;   /* 2px */
    --vendor-radius-base: 0.25rem;  /* 4px */
    --vendor-radius-md: 0.375rem;   /* 6px */
    --vendor-radius-lg: 0.5rem;     /* 8px */
    --vendor-radius-xl: 0.75rem;    /* 12px */
    --vendor-radius-2xl: 1rem;      /* 16px */
    --vendor-radius-3xl: 1.5rem;    /* 24px */
    --vendor-radius-full: 9999px;

    /* === SHADOWS === */
    --vendor-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --vendor-shadow-base: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --vendor-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --vendor-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --vendor-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --vendor-shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    --vendor-shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);
    --vendor-shadow-none: none;

    /* === BRAND SHADOWS === */
    --vendor-shadow-brand: 0 4px 14px 0 rgba(126, 217, 87, 0.15);
    --vendor-shadow-brand-lg: 0 10px 25px -3px rgba(126, 217, 87, 0.2);

    /* === TRANSITIONS === */
    --vendor-transition-fast: all 150ms cubic-bezier(0.4, 0, 0.2, 1);
    --vendor-transition-base: all 250ms cubic-bezier(0.4, 0, 0.2, 1);
    --vendor-transition-slow: all 350ms cubic-bezier(0.4, 0, 0.2, 1);

    /* === Z-INDEX === */
    --vendor-z-dropdown: 1000;
    --vendor-z-sticky: 1020;
    --vendor-z-fixed: 1030;
    --vendor-z-modal-backdrop: 1040;
    --vendor-z-modal: 1050;
    --vendor-z-popover: 1060;
    --vendor-z-tooltip: 1070;
    --vendor-z-toast: 1080;

    /* === BREAKPOINTS === */
    --vendor-screen-sm: 640px;
    --vendor-screen-md: 768px;
    --vendor-screen-lg: 1024px;
    --vendor-screen-xl: 1280px;
    --vendor-screen-2xl: 1536px;
}

/* === LIGHT THEME === */
:root {
    --vendor-bg-primary: #f9fafb;
    --vendor-bg-secondary: var(--vendor-white);
    --vendor-bg-tertiary: var(--vendor-gray-100);
    --vendor-bg-accent: var(--vendor-brand-primary-50);
    
    --vendor-text-primary: var(--vendor-gray-900);
    --vendor-text-secondary: var(--vendor-gray-700);
    --vendor-text-tertiary: var(--vendor-gray-500);
    --vendor-text-inverse: var(--vendor-white);
    
    --vendor-border-primary: var(--vendor-gray-200);
    --vendor-border-secondary: var(--vendor-gray-300);
    --vendor-border-accent: var(--vendor-brand-primary-200);
    
    --vendor-surface-primary: var(--vendor-white);
    --vendor-surface-secondary: var(--vendor-gray-50);
    --vendor-surface-hover: var(--vendor-gray-100);
    --vendor-surface-active: var(--vendor-gray-200);
}

/* === DARK THEME === */
.dark {
    --vendor-bg-primary: var(--vendor-gray-900);
    --vendor-bg-secondary: var(--vendor-gray-800);
    --vendor-bg-tertiary: var(--vendor-gray-700);
    --vendor-bg-accent: rgba(126, 217, 87, 0.1);
    
    --vendor-text-primary: var(--vendor-gray-100);
    --vendor-text-secondary: var(--vendor-gray-300);
    --vendor-text-tertiary: var(--vendor-gray-500);
    --vendor-text-inverse: var(--vendor-gray-900);
    
    --vendor-border-primary: var(--vendor-gray-700);
    --vendor-border-secondary: var(--vendor-gray-600);
    --vendor-border-accent: rgba(126, 217, 87, 0.3);
    
    --vendor-surface-primary: var(--vendor-gray-800);
    --vendor-surface-secondary: var(--vendor-gray-700);
    --vendor-surface-hover: var(--vendor-gray-600);
    --vendor-surface-active: var(--vendor-gray-500);
}

/* === BASE STYLES === */
*,
*::before,
*::after {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html {
    line-height: 1.15;
    -webkit-text-size-adjust: 100%;
    -webkit-tap-highlight-color: transparent;
}

body {
    margin: 0;
    font-family: var(--vendor-font-family-primary);
    font-size: var(--vendor-text-base);
    font-weight: var(--vendor-font-normal);
    line-height: var(--vendor-leading-normal);
    color: var(--vendor-text-primary);
    background-color: var(--vendor-bg-primary);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* === MODERN VENDOR BODY === */
.modern-vendor-body {
    font-family: var(--vendor-font-family-primary);
    background: var(--vendor-bg-primary);
    color: var(--vendor-text-primary);
    margin: 0;
    padding: 0;
    min-height: 100vh;
    overflow-x: hidden;
    line-height: var(--vendor-leading-normal);
    transition: var(--vendor-transition-base);
}

/* === TYPOGRAPHY === */
h1, h2, h3, h4, h5, h6 {
    margin: 0;
    font-weight: var(--vendor-font-semibold);
    line-height: var(--vendor-leading-tight);
    color: var(--vendor-text-primary);
}

h1 { font-size: var(--vendor-text-3xl); }
h2 { font-size: var(--vendor-text-2xl); }
h3 { font-size: var(--vendor-text-xl); }
h4 { font-size: var(--vendor-text-lg); }
h5 { font-size: var(--vendor-text-base); }
h6 { font-size: var(--vendor-text-sm); }

p {
    margin: 0;
    color: var(--vendor-text-secondary);
    line-height: var(--vendor-leading-relaxed);
}

a {
    color: var(--vendor-brand-primary);
    text-decoration: none;
    transition: var(--vendor-transition-fast);
}

a:hover {
    color: var(--vendor-brand-primary-700);
}

/* === FOCUS STYLES === */
*:focus {
    outline: 2px solid var(--vendor-brand-primary);
    outline-offset: 2px;
}

*:focus:not(:focus-visible) {
    outline: none;
}

/* === SCROLLBAR STYLES === */
::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

::-webkit-scrollbar-track {
    background: var(--vendor-surface-secondary);
}

::-webkit-scrollbar-thumb {
    background: var(--vendor-border-secondary);
    border-radius: var(--vendor-radius-full);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--vendor-brand-primary);
}

/* === SELECTION STYLES === */
::selection {
    background-color: var(--vendor-brand-primary-200);
    color: var(--vendor-text-primary);
}

::-moz-selection {
    background-color: var(--vendor-brand-primary-200);
    color: var(--vendor-text-primary);
}
