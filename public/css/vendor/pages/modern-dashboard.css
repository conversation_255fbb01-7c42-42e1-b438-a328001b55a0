/* ===== MODERN VENDOR DASHBOARD CSS ===== */

/* === WELCOME SECTION === */
.modern-welcome-section {
    background: linear-gradient(135deg, var(--vendor-brand-primary-50), var(--vendor-white));
    border-radius: var(--vendor-radius-2xl);
    padding: var(--vendor-space-8);
    margin-bottom: var(--vendor-space-8);
    border: 1px solid var(--vendor-brand-primary-200);
    position: relative;
    overflow: hidden;
}

.modern-welcome-section::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(126, 217, 87, 0.1) 0%, transparent 70%);
    pointer-events: none;
}

.welcome-container {
    display: grid;
    grid-template-columns: 1fr auto;
    gap: var(--vendor-space-8);
    align-items: center;
    position: relative;
    z-index: 1;
}

.welcome-content {
    display: flex;
    flex-direction: column;
    gap: var(--vendor-space-6);
}

.welcome-header {
    display: flex;
    flex-direction: column;
    gap: var(--vendor-space-4);
}

.welcome-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--vendor-space-2);
    padding: var(--vendor-space-2) var(--vendor-space-4);
    background: linear-gradient(135deg, var(--vendor-brand-primary), var(--vendor-brand-primary-700));
    color: var(--vendor-white);
    border-radius: var(--vendor-radius-full);
    font-size: var(--vendor-text-sm);
    font-weight: var(--vendor-font-semibold);
    width: fit-content;
    box-shadow: var(--vendor-shadow-brand);
}

.welcome-title {
    font-size: var(--vendor-text-4xl);
    font-weight: var(--vendor-font-bold);
    color: var(--vendor-text-primary);
    line-height: var(--vendor-leading-tight);
    margin: 0;
}

.welcome-title .highlight {
    background: linear-gradient(135deg, var(--vendor-brand-primary), var(--vendor-brand-primary-700));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.welcome-description {
    font-size: var(--vendor-text-lg);
    color: var(--vendor-text-secondary);
    line-height: var(--vendor-leading-relaxed);
    margin: 0;
    max-width: 600px;
}

.welcome-actions {
    display: flex;
    align-items: center;
    gap: var(--vendor-space-4);
    flex-wrap: wrap;
}

/* === WELCOME VISUAL === */
.welcome-visual {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.visual-container {
    position: relative;
    width: 200px;
    height: 200px;
}

.floating-elements {
    position: absolute;
    inset: 0;
}

.floating-card {
    position: absolute;
    background: var(--vendor-white);
    border: 1px solid var(--vendor-border-primary);
    border-radius: var(--vendor-radius-lg);
    padding: var(--vendor-space-3);
    box-shadow: var(--vendor-shadow-lg);
    display: flex;
    align-items: center;
    gap: var(--vendor-space-2);
    font-size: var(--vendor-text-sm);
    font-weight: var(--vendor-font-semibold);
    animation: float 3s ease-in-out infinite;
}

.floating-card.card-1 {
    top: 20px;
    right: 10px;
    color: var(--vendor-brand-primary);
    animation-delay: 0s;
}

.floating-card.card-2 {
    bottom: 60px;
    left: -10px;
    color: var(--vendor-success);
    animation-delay: 1s;
}

.floating-card.card-3 {
    top: 80px;
    left: 20px;
    color: var(--vendor-info);
    animation-delay: 2s;
}

.main-visual {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--vendor-brand-primary), var(--vendor-brand-primary-700));
    border-radius: var(--vendor-radius-2xl);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--vendor-shadow-brand-lg);
}

.store-icon {
    font-size: var(--vendor-text-3xl);
    color: var(--vendor-white);
}

/* === STATS SECTION === */
.modern-stats-section {
    margin-bottom: var(--vendor-space-8);
}

.stats-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--vendor-space-6);
}

.stats-title {
    font-size: var(--vendor-text-2xl);
    font-weight: var(--vendor-font-bold);
    color: var(--vendor-text-primary);
    margin: 0;
}

.stats-controls {
    display: flex;
    align-items: center;
    gap: var(--vendor-space-4);
}

.time-selector {
    display: flex;
    background: var(--vendor-surface-secondary);
    border-radius: var(--vendor-radius-lg);
    padding: var(--vendor-space-1);
    border: 1px solid var(--vendor-border-primary);
}

.time-btn {
    padding: var(--vendor-space-2) var(--vendor-space-4);
    border: none;
    background: transparent;
    border-radius: var(--vendor-radius-md);
    font-size: var(--vendor-text-sm);
    font-weight: var(--vendor-font-medium);
    color: var(--vendor-text-secondary);
    cursor: pointer;
    transition: var(--vendor-transition-fast);
}

.time-btn.active,
.time-btn:hover {
    background: var(--vendor-brand-primary);
    color: var(--vendor-white);
    box-shadow: var(--vendor-shadow-sm);
}

/* === STAT CARD ENHANCEMENTS === */
.stat-menu-container {
    position: relative;
}

.stat-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    margin-top: var(--vendor-space-2);
    background: var(--vendor-surface-primary);
    border: 1px solid var(--vendor-border-primary);
    border-radius: var(--vendor-radius-lg);
    box-shadow: var(--vendor-shadow-xl);
    z-index: var(--vendor-z-dropdown);
    min-width: 160px;
    overflow: hidden;
}

.stat-dropdown-item {
    display: flex;
    align-items: center;
    gap: var(--vendor-space-3);
    padding: var(--vendor-space-3) var(--vendor-space-4);
    color: var(--vendor-text-secondary);
    text-decoration: none;
    font-size: var(--vendor-text-sm);
    font-weight: var(--vendor-font-medium);
    transition: var(--vendor-transition-fast);
}

.stat-dropdown-item:hover {
    background: var(--vendor-surface-hover);
    color: var(--vendor-text-primary);
}

.stat-dropdown-item i {
    width: 16px;
    text-align: center;
    color: var(--vendor-text-tertiary);
}

/* === CHARTS SECTION === */
.modern-charts-section {
    margin-bottom: var(--vendor-space-8);
}

.charts-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: var(--vendor-space-6);
}

.chart-card {
    background: var(--vendor-surface-primary);
    border: 1px solid var(--vendor-border-primary);
    border-radius: var(--vendor-radius-xl);
    overflow: hidden;
    box-shadow: var(--vendor-shadow-sm);
    transition: var(--vendor-transition-fast);
}

.chart-card:hover {
    box-shadow: var(--vendor-shadow-md);
    transform: translateY(-1px);
}

.chart-header {
    padding: var(--vendor-space-6);
    border-bottom: 1px solid var(--vendor-border-primary);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.chart-title-section {
    flex: 1;
}

.chart-title {
    font-size: var(--vendor-text-lg);
    font-weight: var(--vendor-font-semibold);
    color: var(--vendor-text-primary);
    margin: 0 0 var(--vendor-space-1);
}

.chart-subtitle {
    font-size: var(--vendor-text-sm);
    color: var(--vendor-text-tertiary);
    margin: 0;
}

.chart-controls {
    display: flex;
    background: var(--vendor-surface-secondary);
    border-radius: var(--vendor-radius-lg);
    padding: var(--vendor-space-1);
    border: 1px solid var(--vendor-border-primary);
}

.chart-control {
    padding: var(--vendor-space-2) var(--vendor-space-3);
    border: none;
    background: transparent;
    border-radius: var(--vendor-radius-md);
    font-size: var(--vendor-text-xs);
    font-weight: var(--vendor-font-medium);
    color: var(--vendor-text-secondary);
    cursor: pointer;
    transition: var(--vendor-transition-fast);
}

.chart-control.active,
.chart-control:hover {
    background: var(--vendor-brand-primary);
    color: var(--vendor-white);
}

.chart-body {
    padding: var(--vendor-space-6);
}

.chart-container {
    margin-bottom: var(--vendor-space-4);
}

.chart-insights {
    display: flex;
    justify-content: space-around;
    padding-top: var(--vendor-space-4);
    border-top: 1px solid var(--vendor-border-primary);
}

.insight-item {
    text-align: center;
}

.insight-label {
    font-size: var(--vendor-text-xs);
    color: var(--vendor-text-tertiary);
    margin-bottom: var(--vendor-space-1);
    font-weight: var(--vendor-font-medium);
}

.insight-value {
    font-size: var(--vendor-text-lg);
    font-weight: var(--vendor-font-bold);
    color: var(--vendor-text-primary);
}

.view-all-link {
    display: flex;
    align-items: center;
    gap: var(--vendor-space-2);
    color: var(--vendor-brand-primary);
    text-decoration: none;
    font-size: var(--vendor-text-sm);
    font-weight: var(--vendor-font-medium);
    transition: var(--vendor-transition-fast);
}

.view-all-link:hover {
    color: var(--vendor-brand-primary-700);
    gap: var(--vendor-space-3);
}

/* === ACTIVITY CARD === */
.activity-card .activity-list {
    display: flex;
    flex-direction: column;
    gap: var(--vendor-space-4);
}

.activity-item {
    display: flex;
    align-items: flex-start;
    gap: var(--vendor-space-4);
    padding: var(--vendor-space-4);
    border-radius: var(--vendor-radius-lg);
    transition: var(--vendor-transition-fast);
}

.activity-item:hover {
    background: var(--vendor-surface-hover);
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: var(--vendor-radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--vendor-text-base);
    color: var(--vendor-white);
    flex-shrink: 0;
}

.activity-icon.success {
    background: linear-gradient(135deg, var(--vendor-success), var(--vendor-success-dark));
}

.activity-icon.warning {
    background: linear-gradient(135deg, var(--vendor-warning), var(--vendor-warning-dark));
}

.activity-icon.info {
    background: linear-gradient(135deg, var(--vendor-info), var(--vendor-info-dark));
}

.activity-icon.primary {
    background: linear-gradient(135deg, var(--vendor-brand-primary), var(--vendor-brand-primary-700));
}

.activity-icon.error {
    background: linear-gradient(135deg, var(--vendor-error), var(--vendor-error-dark));
}

.activity-content {
    flex: 1;
    min-width: 0;
}

.activity-title {
    font-size: var(--vendor-text-sm);
    font-weight: var(--vendor-font-semibold);
    color: var(--vendor-text-primary);
    margin-bottom: var(--vendor-space-1);
}

.activity-description {
    font-size: var(--vendor-text-sm);
    color: var(--vendor-text-secondary);
    margin-bottom: var(--vendor-space-2);
}

.activity-meta {
    display: flex;
    align-items: center;
    gap: var(--vendor-space-3);
    font-size: var(--vendor-text-xs);
    color: var(--vendor-text-tertiary);
}

.activity-amount {
    font-weight: var(--vendor-font-semibold);
    color: var(--vendor-success);
}

.activity-rating {
    color: var(--vendor-warning);
}

.activity-stock {
    font-weight: var(--vendor-font-medium);
}

.activity-location {
    font-weight: var(--vendor-font-medium);
}

.activity-time {
    font-weight: var(--vendor-font-medium);
}

.activity-status {
    flex-shrink: 0;
}

/* === ANIMATIONS === */
@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

/* === RESPONSIVE DESIGN === */
@media (max-width: 1024px) {
    .welcome-container {
        grid-template-columns: 1fr;
        text-align: center;
    }
    
    .charts-grid {
        grid-template-columns: 1fr;
    }
    
    .stats-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--vendor-space-4);
    }
}

@media (max-width: 768px) {
    .modern-welcome-section {
        padding: var(--vendor-space-6);
    }
    
    .welcome-title {
        font-size: var(--vendor-text-3xl);
    }
    
    .welcome-description {
        font-size: var(--vendor-text-base);
    }
    
    .welcome-actions {
        justify-content: center;
    }
    
    .visual-container {
        width: 150px;
        height: 150px;
    }
    
    .floating-card {
        font-size: var(--vendor-text-xs);
        padding: var(--vendor-space-2);
    }
    
    .time-selector {
        width: 100%;
        justify-content: center;
    }
}

/* === QUICK ACTIONS SECTION === */
.modern-quick-actions-section {
    margin-bottom: var(--vendor-space-8);
}

.section-header {
    text-align: center;
    margin-bottom: var(--vendor-space-8);
}

.section-title {
    font-size: var(--vendor-text-3xl);
    font-weight: var(--vendor-font-bold);
    color: var(--vendor-text-primary);
    margin: 0 0 var(--vendor-space-2);
}

.section-subtitle {
    font-size: var(--vendor-text-lg);
    color: var(--vendor-text-secondary);
    margin: 0;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.quick-actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--vendor-space-6);
}

.quick-action-card {
    background: var(--vendor-surface-primary);
    border: 1px solid var(--vendor-border-primary);
    border-radius: var(--vendor-radius-xl);
    padding: var(--vendor-space-6);
    text-decoration: none;
    color: inherit;
    transition: var(--vendor-transition-fast);
    display: flex;
    align-items: center;
    gap: var(--vendor-space-4);
    position: relative;
    overflow: hidden;
}

.quick-action-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--vendor-brand-primary);
    transform: scaleX(0);
    transform-origin: left;
    transition: var(--vendor-transition-fast);
}

.quick-action-card.primary::before {
    background: linear-gradient(90deg, var(--vendor-brand-primary), var(--vendor-brand-primary-700));
}

.quick-action-card.success::before {
    background: linear-gradient(90deg, var(--vendor-success), var(--vendor-success-dark));
}

.quick-action-card.info::before {
    background: linear-gradient(90deg, var(--vendor-info), var(--vendor-info-dark));
}

.quick-action-card.warning::before {
    background: linear-gradient(90deg, var(--vendor-warning), var(--vendor-warning-dark));
}

.quick-action-card.secondary::before {
    background: linear-gradient(90deg, var(--vendor-gray-500), var(--vendor-gray-700));
}

.quick-action-card.error::before {
    background: linear-gradient(90deg, var(--vendor-error), var(--vendor-error-dark));
}

.quick-action-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--vendor-shadow-lg);
}

.quick-action-card:hover::before {
    transform: scaleX(1);
}

.quick-action-icon {
    width: 56px;
    height: 56px;
    border-radius: var(--vendor-radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--vendor-text-xl);
    color: var(--vendor-white);
    flex-shrink: 0;
    position: relative;
}

.quick-action-card.primary .quick-action-icon {
    background: linear-gradient(135deg, var(--vendor-brand-primary), var(--vendor-brand-primary-700));
}

.quick-action-card.success .quick-action-icon {
    background: linear-gradient(135deg, var(--vendor-success), var(--vendor-success-dark));
}

.quick-action-card.info .quick-action-icon {
    background: linear-gradient(135deg, var(--vendor-info), var(--vendor-info-dark));
}

.quick-action-card.warning .quick-action-icon {
    background: linear-gradient(135deg, var(--vendor-warning), var(--vendor-warning-dark));
}

.quick-action-card.secondary .quick-action-icon {
    background: linear-gradient(135deg, var(--vendor-gray-500), var(--vendor-gray-700));
}

.quick-action-card.error .quick-action-icon {
    background: linear-gradient(135deg, var(--vendor-error), var(--vendor-error-dark));
}

.quick-action-content {
    flex: 1;
    min-width: 0;
}

.quick-action-title {
    font-size: var(--vendor-text-lg);
    font-weight: var(--vendor-font-semibold);
    color: var(--vendor-text-primary);
    margin: 0 0 var(--vendor-space-2);
}

.quick-action-description {
    font-size: var(--vendor-text-sm);
    color: var(--vendor-text-secondary);
    margin: 0;
    line-height: var(--vendor-leading-relaxed);
}

.quick-action-arrow {
    color: var(--vendor-text-tertiary);
    font-size: var(--vendor-text-lg);
    transition: var(--vendor-transition-fast);
}

.quick-action-card:hover .quick-action-arrow {
    color: var(--vendor-brand-primary);
    transform: translateX(4px);
}

/* === ORDERS SECTION === */
.modern-orders-section {
    margin-bottom: var(--vendor-space-8);
}

.section-title-group {
    flex: 1;
}

.section-actions {
    display: flex;
    align-items: center;
    gap: var(--vendor-space-4);
}

.orders-container {
    background: var(--vendor-surface-primary);
    border: 1px solid var(--vendor-border-primary);
    border-radius: var(--vendor-radius-xl);
    overflow: hidden;
    box-shadow: var(--vendor-shadow-sm);
}

.orders-list {
    display: flex;
    flex-direction: column;
}

.order-item {
    display: flex;
    align-items: center;
    gap: var(--vendor-space-4);
    padding: var(--vendor-space-6);
    border-bottom: 1px solid var(--vendor-border-primary);
    transition: var(--vendor-transition-fast);
}

.order-item:last-child {
    border-bottom: none;
}

.order-item:hover {
    background: var(--vendor-surface-hover);
}

.order-info {
    flex: 1;
    min-width: 0;
}

.order-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--vendor-space-3);
}

.order-id {
    font-size: var(--vendor-text-sm);
    font-weight: var(--vendor-font-semibold);
    color: var(--vendor-brand-primary);
}

.order-time {
    font-size: var(--vendor-text-xs);
    color: var(--vendor-text-tertiary);
    font-weight: var(--vendor-font-medium);
}

.order-customer {
    display: flex;
    align-items: center;
    gap: var(--vendor-space-3);
}

.customer-avatar {
    width: 40px;
    height: 40px;
    border-radius: var(--vendor-radius-full);
    overflow: hidden;
    flex-shrink: 0;
}

.customer-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.customer-details {
    flex: 1;
    min-width: 0;
}

.customer-name {
    font-size: var(--vendor-text-sm);
    font-weight: var(--vendor-font-semibold);
    color: var(--vendor-text-primary);
    margin-bottom: var(--vendor-space-0-5);
}

.customer-location {
    font-size: var(--vendor-text-xs);
    color: var(--vendor-text-tertiary);
}

.order-details {
    text-align: center;
    min-width: 100px;
}

.order-amount {
    font-size: var(--vendor-text-lg);
    font-weight: var(--vendor-font-bold);
    color: var(--vendor-text-primary);
    margin-bottom: var(--vendor-space-1);
}

.order-items {
    font-size: var(--vendor-text-xs);
    color: var(--vendor-text-tertiary);
    font-weight: var(--vendor-font-medium);
}

.order-status {
    min-width: 120px;
    text-align: center;
}

.order-actions {
    position: relative;
    flex-shrink: 0;
}

.order-menu-btn {
    width: 32px;
    height: 32px;
    border: none;
    background: var(--vendor-surface-hover);
    border-radius: var(--vendor-radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: var(--vendor-text-tertiary);
    transition: var(--vendor-transition-fast);
}

.order-menu-btn:hover {
    background: var(--vendor-brand-primary-100);
    color: var(--vendor-brand-primary);
}

.order-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    margin-top: var(--vendor-space-2);
    background: var(--vendor-surface-primary);
    border: 1px solid var(--vendor-border-primary);
    border-radius: var(--vendor-radius-lg);
    box-shadow: var(--vendor-shadow-xl);
    z-index: var(--vendor-z-dropdown);
    min-width: 160px;
    overflow: hidden;
}

.order-dropdown-item {
    display: flex;
    align-items: center;
    gap: var(--vendor-space-3);
    padding: var(--vendor-space-3) var(--vendor-space-4);
    color: var(--vendor-text-secondary);
    text-decoration: none;
    font-size: var(--vendor-text-sm);
    font-weight: var(--vendor-font-medium);
    transition: var(--vendor-transition-fast);
}

.order-dropdown-item:hover {
    background: var(--vendor-surface-hover);
    color: var(--vendor-text-primary);
}

.order-dropdown-item i {
    width: 16px;
    text-align: center;
    color: var(--vendor-text-tertiary);
}

/* === INSIGHTS SECTION === */
.modern-insights-section {
    margin-bottom: var(--vendor-space-8);
}

.insights-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--vendor-space-6);
}

.insight-card {
    background: var(--vendor-surface-primary);
    border: 1px solid var(--vendor-border-primary);
    border-radius: var(--vendor-radius-xl);
    overflow: hidden;
    box-shadow: var(--vendor-shadow-sm);
    transition: var(--vendor-transition-fast);
}

.insight-card:hover {
    box-shadow: var(--vendor-shadow-md);
    transform: translateY(-1px);
}

.insight-header {
    padding: var(--vendor-space-6);
    border-bottom: 1px solid var(--vendor-border-primary);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.insight-title-section {
    flex: 1;
}

.insight-title {
    font-size: var(--vendor-text-lg);
    font-weight: var(--vendor-font-semibold);
    color: var(--vendor-text-primary);
    margin: 0 0 var(--vendor-space-1);
}

.insight-subtitle {
    font-size: var(--vendor-text-sm);
    color: var(--vendor-text-tertiary);
    margin: 0;
}

.insight-body {
    padding: var(--vendor-space-6);
}

/* === PRODUCTS LIST === */
.products-list {
    display: flex;
    flex-direction: column;
    gap: var(--vendor-space-4);
}

.product-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--vendor-space-4);
    border-radius: var(--vendor-radius-lg);
    transition: var(--vendor-transition-fast);
}

.product-item:hover {
    background: var(--vendor-surface-hover);
}

.product-info {
    display: flex;
    align-items: center;
    gap: var(--vendor-space-3);
    flex: 1;
    min-width: 0;
}

.product-image {
    width: 40px;
    height: 40px;
    border-radius: var(--vendor-radius-lg);
    overflow: hidden;
    flex-shrink: 0;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.product-details {
    flex: 1;
    min-width: 0;
}

.product-name {
    font-size: var(--vendor-text-sm);
    font-weight: var(--vendor-font-semibold);
    color: var(--vendor-text-primary);
    margin-bottom: var(--vendor-space-1);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.product-meta {
    font-size: var(--vendor-text-xs);
    color: var(--vendor-text-tertiary);
    font-weight: var(--vendor-font-medium);
}

.product-trend {
    display: flex;
    align-items: center;
    gap: var(--vendor-space-1);
    font-size: var(--vendor-text-xs);
    font-weight: var(--vendor-font-semibold);
    padding: var(--vendor-space-1) var(--vendor-space-2);
    border-radius: var(--vendor-radius-full);
}

.product-trend.positive {
    color: var(--vendor-success);
    background: var(--vendor-success-light);
}

.product-trend.negative {
    color: var(--vendor-error);
    background: var(--vendor-error-light);
}

/* === PERFORMANCE CARD === */
.performance-card {
    grid-column: span 1;
}

.performance-controls {
    display: flex;
    background: var(--vendor-surface-secondary);
    border-radius: var(--vendor-radius-lg);
    padding: var(--vendor-space-1);
    border: 1px solid var(--vendor-border-primary);
}

.period-btn {
    padding: var(--vendor-space-2) var(--vendor-space-3);
    border: none;
    background: transparent;
    border-radius: var(--vendor-radius-md);
    font-size: var(--vendor-text-xs);
    font-weight: var(--vendor-font-medium);
    color: var(--vendor-text-secondary);
    cursor: pointer;
    transition: var(--vendor-transition-fast);
}

.period-btn.active,
.period-btn:hover {
    background: var(--vendor-brand-primary);
    color: var(--vendor-white);
}

.performance-chart {
    margin-bottom: var(--vendor-space-6);
}

.performance-summary {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--vendor-space-4);
    padding-top: var(--vendor-space-4);
    border-top: 1px solid var(--vendor-border-primary);
}

.summary-item {
    text-align: center;
}

.summary-label {
    font-size: var(--vendor-text-xs);
    color: var(--vendor-text-tertiary);
    margin-bottom: var(--vendor-space-1);
    font-weight: var(--vendor-font-medium);
}

.summary-value {
    font-size: var(--vendor-text-lg);
    font-weight: var(--vendor-font-bold);
    color: var(--vendor-text-primary);
    margin-bottom: var(--vendor-space-1);
}

.summary-change {
    font-size: var(--vendor-text-xs);
    font-weight: var(--vendor-font-semibold);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--vendor-space-1);
}

.summary-change.positive {
    color: var(--vendor-success);
}

.summary-change.negative {
    color: var(--vendor-error);
}

/* === RESPONSIVE DESIGN FOR NEW SECTIONS === */
@media (max-width: 1024px) {
    .quick-actions-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    }

    .insights-grid {
        grid-template-columns: 1fr;
    }

    .performance-summary {
        grid-template-columns: 1fr;
        gap: var(--vendor-space-3);
    }
}

@media (max-width: 768px) {
    .quick-actions-grid {
        grid-template-columns: 1fr;
    }

    .quick-action-card {
        padding: var(--vendor-space-4);
    }

    .quick-action-icon {
        width: 48px;
        height: 48px;
        font-size: var(--vendor-text-lg);
    }

    .order-item {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--vendor-space-3);
    }

    .order-header {
        width: 100%;
    }

    .order-customer {
        width: 100%;
    }

    .order-details,
    .order-status,
    .order-actions {
        width: 100%;
        text-align: left;
        min-width: auto;
    }

    .order-status {
        display: flex;
        justify-content: flex-start;
    }

    .product-item {
        padding: var(--vendor-space-3);
    }

    .section-header {
        text-align: left;
    }

    .section-title {
        font-size: var(--vendor-text-2xl);
    }

    .section-subtitle {
        font-size: var(--vendor-text-base);
    }
}

@media (max-width: 480px) {
    .order-item {
        padding: var(--vendor-space-4);
    }

    .customer-avatar {
        width: 32px;
        height: 32px;
    }

    .order-amount {
        font-size: var(--vendor-text-base);
    }

    .quick-action-card {
        flex-direction: column;
        text-align: center;
        gap: var(--vendor-space-3);
    }

    .quick-action-arrow {
        display: none;
    }
}
