/* ===== VENDOR ORDERS PAGE CSS ===== */

/* === PAGE HEADER === */
.orders-header {
    background: var(--vendor-surface-primary);
    border: 1px solid var(--vendor-border-primary);
    border-radius: var(--vendor-radius-xl);
    padding: var(--vendor-space-6);
    margin-bottom: var(--vendor-space-6);
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: var(--vendor-space-4);
}

@media (max-width: 768px) {
    .orders-header {
        flex-direction: column;
        align-items: stretch;
        gap: var(--vendor-space-4);
    }
}

.orders-header-left {
    flex: 1;
}

.orders-title {
    font-size: var(--vendor-text-2xl);
    font-weight: var(--vendor-font-bold);
    color: var(--vendor-text-primary);
    margin-bottom: var(--vendor-space-2);
}

.orders-subtitle {
    font-size: var(--vendor-text-base);
    color: var(--vendor-text-secondary);
}

.orders-header-right {
    display: flex;
    gap: var(--vendor-space-3);
}

@media (max-width: 640px) {
    .orders-header-right {
        flex-direction: column;
    }
}

/* === ORDERS STATS === */
.orders-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--vendor-space-4);
    margin-bottom: var(--vendor-space-6);
}

.orders-stat-card {
    background: var(--vendor-surface-primary);
    border: 1px solid var(--vendor-border-primary);
    border-radius: var(--vendor-radius-lg);
    padding: var(--vendor-space-5);
    text-align: center;
    transition: var(--vendor-transition-fast);
}

.orders-stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--vendor-shadow-md);
}

.stat-icon {
    width: 48px;
    height: 48px;
    border-radius: var(--vendor-radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--vendor-space-3);
    font-size: var(--vendor-text-xl);
    color: var(--vendor-white);
}

.stat-icon.pending {
    background: var(--vendor-warning);
}

.stat-icon.processing {
    background: var(--vendor-info);
}

.stat-icon.shipped {
    background: var(--vendor-brand-primary);
}

.stat-icon.delivered {
    background: var(--vendor-success);
}

.stat-value {
    font-size: var(--vendor-text-2xl);
    font-weight: var(--vendor-font-bold);
    color: var(--vendor-text-primary);
    margin-bottom: var(--vendor-space-1);
}

.stat-label {
    font-size: var(--vendor-text-sm);
    color: var(--vendor-text-secondary);
    font-weight: var(--vendor-font-medium);
}

/* === FILTERS === */
.orders-filters {
    background: var(--vendor-surface-primary);
    border: 1px solid var(--vendor-border-primary);
    border-radius: var(--vendor-radius-lg);
    padding: var(--vendor-space-5);
    margin-bottom: var(--vendor-space-6);
}

.filters-grid {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr auto;
    gap: var(--vendor-space-4);
    align-items: end;
}

@media (max-width: 1024px) {
    .filters-grid {
        grid-template-columns: 1fr 1fr;
        gap: var(--vendor-space-3);
    }
}

@media (max-width: 640px) {
    .filters-grid {
        grid-template-columns: 1fr;
        gap: var(--vendor-space-3);
    }
}

/* === ORDERS TABLE === */
.orders-table-container {
    background: var(--vendor-surface-primary);
    border: 1px solid var(--vendor-border-primary);
    border-radius: var(--vendor-radius-xl);
    overflow: hidden;
}

.orders-table {
    width: 100%;
    border-collapse: collapse;
}

.orders-table th {
    background: var(--vendor-surface-secondary);
    padding: var(--vendor-space-4) var(--vendor-space-5);
    text-align: left;
    font-size: var(--vendor-text-sm);
    font-weight: var(--vendor-font-semibold);
    color: var(--vendor-text-primary);
    border-bottom: 1px solid var(--vendor-border-primary);
}

.orders-table td {
    padding: var(--vendor-space-4) var(--vendor-space-5);
    border-bottom: 1px solid var(--vendor-border-primary);
    font-size: var(--vendor-text-sm);
    color: var(--vendor-text-secondary);
}

.orders-table tr:last-child td {
    border-bottom: none;
}

.orders-table tr:hover {
    background: var(--vendor-surface-hover);
}

/* === ORDER CARD === */
.order-card {
    background: var(--vendor-surface-primary);
    border: 1px solid var(--vendor-border-primary);
    border-radius: var(--vendor-radius-lg);
    padding: var(--vendor-space-5);
    margin-bottom: var(--vendor-space-4);
    transition: var(--vendor-transition-fast);
}

.order-card:hover {
    box-shadow: var(--vendor-shadow-md);
    border-color: var(--vendor-brand-primary);
}

.order-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--vendor-space-4);
}

.order-info {
    flex: 1;
}

.order-id {
    font-size: var(--vendor-text-lg);
    font-weight: var(--vendor-font-semibold);
    color: var(--vendor-text-primary);
    margin-bottom: var(--vendor-space-1);
}

.order-date {
    font-size: var(--vendor-text-sm);
    color: var(--vendor-text-tertiary);
}

.order-status-badge {
    padding: var(--vendor-space-2) var(--vendor-space-3);
    border-radius: var(--vendor-radius-full);
    font-size: var(--vendor-text-xs);
    font-weight: var(--vendor-font-semibold);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.order-status-badge.pending {
    background: var(--vendor-warning-100);
    color: var(--vendor-warning-700);
}

.order-status-badge.processing {
    background: var(--vendor-info-100);
    color: var(--vendor-info-700);
}

.order-status-badge.shipped {
    background: var(--vendor-brand-primary-100);
    color: var(--vendor-brand-primary-700);
}

.order-status-badge.delivered {
    background: var(--vendor-success-100);
    color: var(--vendor-success-700);
}

.order-status-badge.cancelled {
    background: var(--vendor-error-100);
    color: var(--vendor-error-700);
}

.order-customer {
    display: flex;
    align-items: center;
    gap: var(--vendor-space-3);
    margin-bottom: var(--vendor-space-4);
}

.customer-avatar {
    width: 40px;
    height: 40px;
    border-radius: var(--vendor-radius-full);
    background: var(--vendor-brand-primary);
    color: var(--vendor-white);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: var(--vendor-font-semibold);
    font-size: var(--vendor-text-sm);
}

.customer-info {
    flex: 1;
}

.customer-name {
    font-size: var(--vendor-text-sm);
    font-weight: var(--vendor-font-medium);
    color: var(--vendor-text-primary);
    margin-bottom: 2px;
}

.customer-email {
    font-size: var(--vendor-text-xs);
    color: var(--vendor-text-tertiary);
}

.order-items {
    margin-bottom: var(--vendor-space-4);
}

.order-items-header {
    font-size: var(--vendor-text-sm);
    font-weight: var(--vendor-font-medium);
    color: var(--vendor-text-primary);
    margin-bottom: var(--vendor-space-2);
}

.order-item {
    display: flex;
    align-items: center;
    gap: var(--vendor-space-3);
    padding: var(--vendor-space-2) 0;
    border-bottom: 1px solid var(--vendor-border-primary);
}

.order-item:last-child {
    border-bottom: none;
}

.item-image {
    width: 40px;
    height: 40px;
    border-radius: var(--vendor-radius-md);
    background: var(--vendor-surface-secondary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--vendor-text-tertiary);
    font-size: var(--vendor-text-sm);
}

.item-details {
    flex: 1;
}

.item-name {
    font-size: var(--vendor-text-sm);
    font-weight: var(--vendor-font-medium);
    color: var(--vendor-text-primary);
    margin-bottom: 2px;
}

.item-quantity {
    font-size: var(--vendor-text-xs);
    color: var(--vendor-text-tertiary);
}

.item-price {
    font-size: var(--vendor-text-sm);
    font-weight: var(--vendor-font-semibold);
    color: var(--vendor-text-primary);
}

.order-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: var(--vendor-space-4);
    border-top: 1px solid var(--vendor-border-primary);
}

.order-total {
    font-size: var(--vendor-text-lg);
    font-weight: var(--vendor-font-bold);
    color: var(--vendor-brand-primary);
}

.order-actions {
    display: flex;
    gap: var(--vendor-space-2);
}

.order-action-btn {
    padding: var(--vendor-space-2) var(--vendor-space-3);
    border: 1px solid var(--vendor-border-primary);
    border-radius: var(--vendor-radius-md);
    background: var(--vendor-surface-secondary);
    color: var(--vendor-text-secondary);
    font-size: var(--vendor-text-xs);
    cursor: pointer;
    transition: var(--vendor-transition-fast);
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: var(--vendor-space-1);
}

.order-action-btn:hover {
    background: var(--vendor-brand-primary);
    color: var(--vendor-white);
    border-color: var(--vendor-brand-primary);
}

.order-action-btn.primary {
    background: var(--vendor-brand-primary);
    color: var(--vendor-white);
    border-color: var(--vendor-brand-primary);
}

.order-action-btn.primary:hover {
    background: var(--vendor-brand-primary-600);
    border-color: var(--vendor-brand-primary-600);
}

/* === EMPTY STATE === */
.orders-empty {
    text-align: center;
    padding: var(--vendor-space-16) var(--vendor-space-8);
    background: var(--vendor-surface-primary);
    border: 1px solid var(--vendor-border-primary);
    border-radius: var(--vendor-radius-xl);
}

.empty-icon {
    width: 80px;
    height: 80px;
    background: var(--vendor-surface-secondary);
    border-radius: var(--vendor-radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--vendor-space-6);
    color: var(--vendor-text-tertiary);
    font-size: var(--vendor-text-3xl);
}

.empty-title {
    font-size: var(--vendor-text-xl);
    font-weight: var(--vendor-font-semibold);
    color: var(--vendor-text-primary);
    margin-bottom: var(--vendor-space-3);
}

.empty-description {
    font-size: var(--vendor-text-base);
    color: var(--vendor-text-secondary);
    margin-bottom: var(--vendor-space-6);
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
}

/* === RESPONSIVE === */
@media (max-width: 768px) {
    .orders-table-container {
        display: none;
    }
    
    .order-header {
        flex-direction: column;
        align-items: stretch;
        gap: var(--vendor-space-3);
    }
    
    .order-footer {
        flex-direction: column;
        align-items: stretch;
        gap: var(--vendor-space-3);
    }
    
    .order-actions {
        justify-content: center;
    }
}
