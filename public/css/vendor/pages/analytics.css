/* ===== VENDOR ANALYTICS PAGE CSS ===== */

/* === PAGE HEADER === */
.analytics-header {
    background: var(--vendor-surface-primary);
    border: 1px solid var(--vendor-border-primary);
    border-radius: var(--vendor-radius-xl);
    padding: var(--vendor-space-8);
    margin-bottom: var(--vendor-space-8);
    position: relative;
    overflow: hidden;
}

.analytics-header::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 300px;
    height: 300px;
    background: linear-gradient(135deg, var(--vendor-brand-primary-100), var(--vendor-brand-primary-200));
    border-radius: 50%;
    transform: translate(50%, -50%);
    opacity: 0.5;
}

.header-content {
    position: relative;
    z-index: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: var(--vendor-space-6);
}

@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        text-align: center;
        gap: var(--vendor-space-4);
    }
}

.header-text {
    flex: 1;
}

.analytics-title {
    font-size: var(--vendor-text-3xl);
    font-weight: var(--vendor-font-bold);
    color: var(--vendor-text-primary);
    margin-bottom: var(--vendor-space-2);
    line-height: var(--vendor-leading-tight);
}

.analytics-subtitle {
    font-size: var(--vendor-text-lg);
    color: var(--vendor-text-secondary);
    margin-bottom: var(--vendor-space-6);
}

.date-range-selector {
    display: flex;
    gap: var(--vendor-space-2);
    background: var(--vendor-surface-secondary);
    padding: var(--vendor-space-1);
    border-radius: var(--vendor-radius-lg);
    border: 1px solid var(--vendor-border-primary);
}

.date-range-btn {
    background: none;
    border: none;
    color: var(--vendor-text-secondary);
    padding: var(--vendor-space-2) var(--vendor-space-4);
    border-radius: var(--vendor-radius-md);
    font-size: var(--vendor-text-sm);
    font-weight: var(--vendor-font-medium);
    cursor: pointer;
    transition: var(--vendor-transition-fast);
}

.date-range-btn:hover,
.date-range-btn.active {
    background: var(--vendor-brand-primary);
    color: var(--vendor-white);
}

.header-actions {
    display: flex;
    gap: var(--vendor-space-3);
}

@media (max-width: 640px) {
    .header-actions {
        flex-direction: column;
        width: 100%;
    }
}

/* === KPI GRID === */
.kpi-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--vendor-space-6);
    margin-bottom: var(--vendor-space-8);
}

@media (max-width: 640px) {
    .kpi-grid {
        grid-template-columns: 1fr;
        gap: var(--vendor-space-4);
    }
}

.kpi-card {
    background: var(--vendor-surface-primary);
    border: 1px solid var(--vendor-border-primary);
    border-radius: var(--vendor-radius-xl);
    padding: var(--vendor-space-6);
    transition: var(--vendor-transition-fast);
    position: relative;
    overflow: hidden;
}

.kpi-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--vendor-shadow-lg);
    border-color: var(--vendor-border-accent);
}

.kpi-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--vendor-brand-primary), var(--vendor-brand-primary-400));
    border-radius: var(--vendor-radius-xl) var(--vendor-radius-xl) 0 0;
}

.kpi-card.revenue::before {
    background: linear-gradient(90deg, var(--vendor-success), var(--vendor-success-light));
}

.kpi-card.orders::before {
    background: linear-gradient(90deg, var(--vendor-info), var(--vendor-info-light));
}

.kpi-card.customers::before {
    background: linear-gradient(90deg, var(--vendor-warning), var(--vendor-warning-light));
}

.kpi-card.conversion::before {
    background: linear-gradient(90deg, var(--vendor-error), var(--vendor-error-light));
}

.kpi-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--vendor-space-4);
}

.kpi-icon {
    width: 48px;
    height: 48px;
    border-radius: var(--vendor-radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--vendor-text-xl);
    color: var(--vendor-white);
    background: var(--vendor-brand-primary);
}

.kpi-card.revenue .kpi-icon {
    background: var(--vendor-success);
}

.kpi-card.orders .kpi-icon {
    background: var(--vendor-info);
}

.kpi-card.customers .kpi-icon {
    background: var(--vendor-warning);
}

.kpi-card.conversion .kpi-icon {
    background: var(--vendor-error);
}

.kpi-menu {
    background: none;
    border: none;
    color: var(--vendor-text-tertiary);
    cursor: pointer;
    padding: var(--vendor-space-1);
    border-radius: var(--vendor-radius-base);
    transition: var(--vendor-transition-fast);
}

.kpi-menu:hover {
    background: var(--vendor-surface-hover);
    color: var(--vendor-text-primary);
}

.kpi-content {
    margin-bottom: var(--vendor-space-4);
}

.kpi-label {
    font-size: var(--vendor-text-sm);
    color: var(--vendor-text-tertiary);
    font-weight: var(--vendor-font-medium);
    margin-bottom: var(--vendor-space-1);
}

.kpi-value {
    font-size: var(--vendor-text-3xl);
    font-weight: var(--vendor-font-bold);
    color: var(--vendor-text-primary);
    line-height: var(--vendor-leading-none);
    margin-bottom: var(--vendor-space-2);
}

.kpi-change {
    display: inline-flex;
    align-items: center;
    gap: var(--vendor-space-1);
    font-size: var(--vendor-text-sm);
    font-weight: var(--vendor-font-medium);
    padding: var(--vendor-space-1) var(--vendor-space-2);
    border-radius: var(--vendor-radius-base);
}

.kpi-change.positive {
    background: var(--vendor-success-50);
    color: var(--vendor-success-700);
}

.kpi-change.negative {
    background: var(--vendor-error-50);
    color: var(--vendor-error-700);
}

.kpi-change.neutral {
    background: var(--vendor-gray-100);
    color: var(--vendor-gray-700);
}

.dark .kpi-change.positive {
    background: rgba(16, 185, 129, 0.1);
    color: var(--vendor-success-light);
}

.dark .kpi-change.negative {
    background: rgba(239, 68, 68, 0.1);
    color: var(--vendor-error-light);
}

.dark .kpi-change.neutral {
    background: var(--vendor-gray-800);
    color: var(--vendor-gray-300);
}

.kpi-sparkline {
    height: 40px;
    margin-top: var(--vendor-space-3);
}

/* === CHART GRID === */
.chart-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: var(--vendor-space-6);
    margin-bottom: var(--vendor-space-8);
}

@media (max-width: 1024px) {
    .chart-grid {
        grid-template-columns: 1fr;
        gap: var(--vendor-space-4);
    }
}

.analytics-chart-card {
    background: var(--vendor-surface-primary);
    border: 1px solid var(--vendor-border-primary);
    border-radius: var(--vendor-radius-xl);
    overflow: hidden;
}

.chart-header {
    padding: var(--vendor-space-6);
    border-bottom: 1px solid var(--vendor-border-primary);
    background: var(--vendor-surface-secondary);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.chart-title {
    font-size: var(--vendor-text-lg);
    font-weight: var(--vendor-font-semibold);
    color: var(--vendor-text-primary);
    margin: 0;
}

.chart-controls {
    display: flex;
    gap: var(--vendor-space-2);
}

.chart-control {
    background: var(--vendor-surface-primary);
    border: 1px solid var(--vendor-border-primary);
    color: var(--vendor-text-secondary);
    padding: var(--vendor-space-2) var(--vendor-space-3);
    border-radius: var(--vendor-radius-base);
    font-size: var(--vendor-text-xs);
    cursor: pointer;
    transition: var(--vendor-transition-fast);
}

.chart-control:hover,
.chart-control.active {
    background: var(--vendor-brand-primary);
    color: var(--vendor-white);
    border-color: var(--vendor-brand-primary);
}

.chart-body {
    padding: var(--vendor-space-6);
    min-height: 400px;
}

/* === TOP PRODUCTS === */
.top-products-list {
    display: flex;
    flex-direction: column;
    gap: var(--vendor-space-4);
}

.product-item {
    display: flex;
    align-items: center;
    gap: var(--vendor-space-4);
    padding: var(--vendor-space-4);
    border-radius: var(--vendor-radius-lg);
    transition: var(--vendor-transition-fast);
}

.product-item:hover {
    background: var(--vendor-surface-hover);
}

.product-rank {
    width: 32px;
    height: 32px;
    border-radius: var(--vendor-radius-full);
    background: var(--vendor-brand-primary);
    color: var(--vendor-white);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--vendor-text-sm);
    font-weight: var(--vendor-font-bold);
    flex-shrink: 0;
}

.product-image {
    width: 48px;
    height: 48px;
    border-radius: var(--vendor-radius-md);
    background: var(--vendor-surface-secondary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--vendor-text-tertiary);
    flex-shrink: 0;
}

.product-info {
    flex: 1;
    min-width: 0;
}

.product-name {
    font-size: var(--vendor-text-sm);
    font-weight: var(--vendor-font-medium);
    color: var(--vendor-text-primary);
    margin-bottom: var(--vendor-space-1);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.product-sales {
    font-size: var(--vendor-text-xs);
    color: var(--vendor-text-tertiary);
}

.product-revenue {
    font-size: var(--vendor-text-sm);
    font-weight: var(--vendor-font-semibold);
    color: var(--vendor-text-primary);
    white-space: nowrap;
}

/* === ANALYTICS TABLES === */
.analytics-table-section {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--vendor-space-6);
    margin-bottom: var(--vendor-space-8);
}

@media (max-width: 1024px) {
    .analytics-table-section {
        grid-template-columns: 1fr;
        gap: var(--vendor-space-4);
    }
}

.analytics-table-card {
    background: var(--vendor-surface-primary);
    border: 1px solid var(--vendor-border-primary);
    border-radius: var(--vendor-radius-xl);
    overflow: hidden;
}

.table-header {
    padding: var(--vendor-space-6);
    border-bottom: 1px solid var(--vendor-border-primary);
    background: var(--vendor-surface-secondary);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.table-title {
    font-size: var(--vendor-text-lg);
    font-weight: var(--vendor-font-semibold);
    color: var(--vendor-text-primary);
    margin: 0;
}

.table-body {
    padding: var(--vendor-space-6);
    max-height: 400px;
    overflow-y: auto;
}

.analytics-table {
    width: 100%;
    border-collapse: collapse;
}

.analytics-table th {
    text-align: left;
    font-size: var(--vendor-text-xs);
    font-weight: var(--vendor-font-semibold);
    color: var(--vendor-text-tertiary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    padding-bottom: var(--vendor-space-3);
    border-bottom: 1px solid var(--vendor-border-primary);
}

.analytics-table td {
    padding: var(--vendor-space-3) 0;
    font-size: var(--vendor-text-sm);
    color: var(--vendor-text-secondary);
    border-bottom: 1px solid var(--vendor-border-primary);
}

.analytics-table tr:last-child td {
    border-bottom: none;
}

.analytics-table tr:hover {
    background: var(--vendor-surface-hover);
}

/* === EXPORT SECTION === */
.export-section {
    background: var(--vendor-surface-primary);
    border: 1px solid var(--vendor-border-primary);
    border-radius: var(--vendor-radius-xl);
    padding: var(--vendor-space-6);
    text-align: center;
}

.export-title {
    font-size: var(--vendor-text-xl);
    font-weight: var(--vendor-font-semibold);
    color: var(--vendor-text-primary);
    margin-bottom: var(--vendor-space-2);
}

.export-description {
    font-size: var(--vendor-text-base);
    color: var(--vendor-text-secondary);
    margin-bottom: var(--vendor-space-6);
}

.export-actions {
    display: flex;
    justify-content: center;
    gap: var(--vendor-space-3);
}

@media (max-width: 640px) {
    .export-actions {
        flex-direction: column;
        align-items: center;
    }
}
