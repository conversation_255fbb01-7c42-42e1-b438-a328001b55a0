/* ===== VENDOR CUSTOMERS PAGE CSS ===== */

/* === PAGE HEADER === */
.customers-header {
    background: var(--vendor-surface-primary);
    border: 1px solid var(--vendor-border-primary);
    border-radius: var(--vendor-radius-xl);
    padding: var(--vendor-space-6);
    margin-bottom: var(--vendor-space-6);
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: var(--vendor-space-4);
}

@media (max-width: 768px) {
    .customers-header {
        flex-direction: column;
        align-items: stretch;
        gap: var(--vendor-space-4);
    }
}

.customers-header-left {
    flex: 1;
}

.customers-title {
    font-size: var(--vendor-text-2xl);
    font-weight: var(--vendor-font-bold);
    color: var(--vendor-text-primary);
    margin-bottom: var(--vendor-space-2);
}

.customers-subtitle {
    font-size: var(--vendor-text-base);
    color: var(--vendor-text-secondary);
}

.customers-header-right {
    display: flex;
    gap: var(--vendor-space-3);
}

/* === CUSTOMER STATS === */
.customers-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--vendor-space-4);
    margin-bottom: var(--vendor-space-6);
}

.customers-stat-card {
    background: var(--vendor-surface-primary);
    border: 1px solid var(--vendor-border-primary);
    border-radius: var(--vendor-radius-lg);
    padding: var(--vendor-space-5);
    text-align: center;
    transition: var(--vendor-transition-fast);
}

.customers-stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--vendor-shadow-md);
}

.stat-icon {
    width: 48px;
    height: 48px;
    border-radius: var(--vendor-radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--vendor-space-3);
    font-size: var(--vendor-text-xl);
    color: var(--vendor-white);
}

.stat-icon.total {
    background: var(--vendor-brand-primary);
}

.stat-icon.new {
    background: var(--vendor-success);
}

.stat-icon.active {
    background: var(--vendor-info);
}

.stat-icon.returning {
    background: var(--vendor-warning);
}

.stat-value {
    font-size: var(--vendor-text-2xl);
    font-weight: var(--vendor-font-bold);
    color: var(--vendor-text-primary);
    margin-bottom: var(--vendor-space-1);
}

.stat-label {
    font-size: var(--vendor-text-sm);
    color: var(--vendor-text-secondary);
    font-weight: var(--vendor-font-medium);
}

/* === FILTERS === */
.customers-filters {
    background: var(--vendor-surface-primary);
    border: 1px solid var(--vendor-border-primary);
    border-radius: var(--vendor-radius-lg);
    padding: var(--vendor-space-5);
    margin-bottom: var(--vendor-space-6);
}

.filters-grid {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr auto;
    gap: var(--vendor-space-4);
    align-items: end;
}

@media (max-width: 1024px) {
    .filters-grid {
        grid-template-columns: 1fr 1fr;
        gap: var(--vendor-space-3);
    }
}

@media (max-width: 640px) {
    .filters-grid {
        grid-template-columns: 1fr;
        gap: var(--vendor-space-3);
    }
}

/* === CUSTOMERS GRID === */
.customers-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: var(--vendor-space-5);
    margin-bottom: var(--vendor-space-8);
}

@media (max-width: 640px) {
    .customers-grid {
        grid-template-columns: 1fr;
        gap: var(--vendor-space-4);
    }
}

.customer-card {
    background: var(--vendor-surface-primary);
    border: 1px solid var(--vendor-border-primary);
    border-radius: var(--vendor-radius-lg);
    padding: var(--vendor-space-5);
    transition: var(--vendor-transition-fast);
    position: relative;
}

.customer-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--vendor-shadow-md);
    border-color: var(--vendor-brand-primary);
}

.customer-header {
    display: flex;
    align-items: center;
    gap: var(--vendor-space-4);
    margin-bottom: var(--vendor-space-4);
}

.customer-avatar {
    width: 60px;
    height: 60px;
    border-radius: var(--vendor-radius-full);
    background: var(--vendor-brand-primary);
    color: var(--vendor-white);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--vendor-text-xl);
    font-weight: var(--vendor-font-bold);
    position: relative;
}

.customer-status-dot {
    position: absolute;
    bottom: 2px;
    right: 2px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid var(--vendor-white);
}

.customer-status-dot.active {
    background: var(--vendor-success);
}

.customer-status-dot.inactive {
    background: var(--vendor-error);
}

.customer-info {
    flex: 1;
}

.customer-name {
    font-size: var(--vendor-text-lg);
    font-weight: var(--vendor-font-semibold);
    color: var(--vendor-text-primary);
    margin-bottom: var(--vendor-space-1);
}

.customer-email {
    font-size: var(--vendor-text-sm);
    color: var(--vendor-text-secondary);
    margin-bottom: var(--vendor-space-1);
}

.customer-joined {
    font-size: var(--vendor-text-xs);
    color: var(--vendor-text-tertiary);
}

.customer-actions {
    position: absolute;
    top: var(--vendor-space-4);
    right: var(--vendor-space-4);
    display: flex;
    gap: var(--vendor-space-2);
    opacity: 0;
    transition: var(--vendor-transition-fast);
}

.customer-card:hover .customer-actions {
    opacity: 1;
}

.customer-action-btn {
    width: 32px;
    height: 32px;
    background: var(--vendor-surface-secondary);
    border: 1px solid var(--vendor-border-primary);
    border-radius: var(--vendor-radius-full);
    color: var(--vendor-text-secondary);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--vendor-transition-fast);
    font-size: var(--vendor-text-sm);
}

.customer-action-btn:hover {
    background: var(--vendor-brand-primary);
    color: var(--vendor-white);
    border-color: var(--vendor-brand-primary);
    transform: scale(1.1);
}

.customer-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--vendor-space-4);
    margin-bottom: var(--vendor-space-4);
    padding: var(--vendor-space-4);
    background: var(--vendor-surface-secondary);
    border-radius: var(--vendor-radius-md);
}

.customer-stat {
    text-align: center;
}

.customer-stat-value {
    font-size: var(--vendor-text-lg);
    font-weight: var(--vendor-font-bold);
    color: var(--vendor-text-primary);
    margin-bottom: var(--vendor-space-1);
}

.customer-stat-label {
    font-size: var(--vendor-text-xs);
    color: var(--vendor-text-tertiary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.customer-tags {
    display: flex;
    flex-wrap: wrap;
    gap: var(--vendor-space-2);
    margin-bottom: var(--vendor-space-4);
}

.customer-tag {
    padding: var(--vendor-space-1) var(--vendor-space-2);
    background: var(--vendor-brand-primary-100);
    color: var(--vendor-brand-primary-700);
    border-radius: var(--vendor-radius-full);
    font-size: var(--vendor-text-xs);
    font-weight: var(--vendor-font-medium);
}

.customer-tag.vip {
    background: var(--vendor-warning-100);
    color: var(--vendor-warning-700);
}

.customer-tag.new {
    background: var(--vendor-success-100);
    color: var(--vendor-success-700);
}

.customer-footer {
    display: flex;
    gap: var(--vendor-space-2);
}

.customer-btn {
    flex: 1;
    padding: var(--vendor-space-2) var(--vendor-space-3);
    border: 1px solid var(--vendor-border-primary);
    border-radius: var(--vendor-radius-md);
    background: var(--vendor-surface-secondary);
    color: var(--vendor-text-secondary);
    font-size: var(--vendor-text-sm);
    cursor: pointer;
    transition: var(--vendor-transition-fast);
    text-decoration: none;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--vendor-space-2);
}

.customer-btn:hover {
    background: var(--vendor-brand-primary);
    color: var(--vendor-white);
    border-color: var(--vendor-brand-primary);
}

.customer-btn.primary {
    background: var(--vendor-brand-primary);
    color: var(--vendor-white);
    border-color: var(--vendor-brand-primary);
}

.customer-btn.primary:hover {
    background: var(--vendor-brand-primary-600);
    border-color: var(--vendor-brand-primary-600);
}

/* === EMPTY STATE === */
.customers-empty {
    text-align: center;
    padding: var(--vendor-space-16) var(--vendor-space-8);
    background: var(--vendor-surface-primary);
    border: 1px solid var(--vendor-border-primary);
    border-radius: var(--vendor-radius-xl);
}

.empty-icon {
    width: 80px;
    height: 80px;
    background: var(--vendor-surface-secondary);
    border-radius: var(--vendor-radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--vendor-space-6);
    color: var(--vendor-text-tertiary);
    font-size: var(--vendor-text-3xl);
}

.empty-title {
    font-size: var(--vendor-text-xl);
    font-weight: var(--vendor-font-semibold);
    color: var(--vendor-text-primary);
    margin-bottom: var(--vendor-space-3);
}

.empty-description {
    font-size: var(--vendor-text-base);
    color: var(--vendor-text-secondary);
    margin-bottom: var(--vendor-space-6);
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
}

/* === RESPONSIVE === */
@media (max-width: 768px) {
    .customers-header-right {
        flex-direction: column;
    }
    
    .customer-stats {
        grid-template-columns: 1fr;
        gap: var(--vendor-space-2);
    }
    
    .customer-footer {
        flex-direction: column;
    }
}
