/* ===== VENDOR SETTINGS PAGE CSS ===== */

/* === PAGE HEADER === */
.settings-header {
    background: var(--vendor-surface-primary);
    border: 1px solid var(--vendor-border-primary);
    border-radius: var(--vendor-radius-xl);
    padding: var(--vendor-space-6);
    margin-bottom: var(--vendor-space-6);
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: var(--vendor-space-4);
}

@media (max-width: 768px) {
    .settings-header {
        flex-direction: column;
        align-items: stretch;
        gap: var(--vendor-space-4);
    }
}

.settings-header-left {
    flex: 1;
}

.settings-title {
    font-size: var(--vendor-text-2xl);
    font-weight: var(--vendor-font-bold);
    color: var(--vendor-text-primary);
    margin-bottom: var(--vendor-space-2);
}

.settings-subtitle {
    font-size: var(--vendor-text-base);
    color: var(--vendor-text-secondary);
}

.settings-header-right {
    display: flex;
    gap: var(--vendor-space-3);
}

/* === SETTINGS LAYOUT === */
.settings-layout {
    display: grid;
    grid-template-columns: 280px 1fr;
    gap: var(--vendor-space-6);
}

@media (max-width: 1024px) {
    .settings-layout {
        grid-template-columns: 1fr;
        gap: var(--vendor-space-4);
    }
}

/* === SETTINGS SIDEBAR === */
.settings-sidebar {
    background: var(--vendor-surface-primary);
    border: 1px solid var(--vendor-border-primary);
    border-radius: var(--vendor-radius-lg);
    padding: var(--vendor-space-5);
    height: fit-content;
    position: sticky;
    top: var(--vendor-space-6);
}

@media (max-width: 1024px) {
    .settings-sidebar {
        position: static;
        order: 2;
    }
}

.settings-nav {
    display: flex;
    flex-direction: column;
    gap: var(--vendor-space-1);
}

.settings-nav-item {
    display: flex;
    align-items: center;
    gap: var(--vendor-space-3);
    padding: var(--vendor-space-3) var(--vendor-space-4);
    border-radius: var(--vendor-radius-md);
    color: var(--vendor-text-secondary);
    text-decoration: none;
    transition: var(--vendor-transition-fast);
    cursor: pointer;
}

.settings-nav-item:hover {
    background: var(--vendor-surface-hover);
    color: var(--vendor-text-primary);
}

.settings-nav-item.active {
    background: var(--vendor-brand-primary-100);
    color: var(--vendor-brand-primary);
    font-weight: var(--vendor-font-medium);
}

.settings-nav-icon {
    font-size: var(--vendor-text-base);
    width: 20px;
    text-align: center;
}

/* === SETTINGS CONTENT === */
.settings-content {
    background: var(--vendor-surface-primary);
    border: 1px solid var(--vendor-border-primary);
    border-radius: var(--vendor-radius-lg);
    overflow: hidden;
}

@media (max-width: 1024px) {
    .settings-content {
        order: 1;
    }
}

.settings-section {
    display: none;
    padding: var(--vendor-space-6);
}

.settings-section.active {
    display: block;
}

.settings-section-header {
    margin-bottom: var(--vendor-space-6);
    padding-bottom: var(--vendor-space-4);
    border-bottom: 1px solid var(--vendor-border-primary);
}

.settings-section-title {
    font-size: var(--vendor-text-xl);
    font-weight: var(--vendor-font-semibold);
    color: var(--vendor-text-primary);
    margin-bottom: var(--vendor-space-2);
}

.settings-section-description {
    font-size: var(--vendor-text-base);
    color: var(--vendor-text-secondary);
}

/* === FORM GROUPS === */
.form-group {
    margin-bottom: var(--vendor-space-6);
}

.form-group:last-child {
    margin-bottom: 0;
}

.form-label {
    display: block;
    font-size: var(--vendor-text-sm);
    font-weight: var(--vendor-font-medium);
    color: var(--vendor-text-primary);
    margin-bottom: var(--vendor-space-2);
}

.form-label.required::after {
    content: ' *';
    color: var(--vendor-error);
}

.form-input,
.form-textarea,
.form-select {
    width: 100%;
    padding: var(--vendor-space-3);
    border: 1px solid var(--vendor-border-primary);
    border-radius: var(--vendor-radius-md);
    background: var(--vendor-surface-secondary);
    color: var(--vendor-text-primary);
    font-size: var(--vendor-text-sm);
    transition: var(--vendor-transition-fast);
}

.form-input:focus,
.form-textarea:focus,
.form-select:focus {
    outline: none;
    border-color: var(--vendor-brand-primary);
    box-shadow: 0 0 0 3px rgba(126, 217, 87, 0.1);
}

.form-textarea {
    min-height: 100px;
    resize: vertical;
}

.form-help {
    font-size: var(--vendor-text-xs);
    color: var(--vendor-text-tertiary);
    margin-top: var(--vendor-space-1);
}

.form-error {
    font-size: var(--vendor-text-xs);
    color: var(--vendor-error);
    margin-top: var(--vendor-space-1);
}

/* === FORM GRID === */
.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--vendor-space-4);
}

.form-grid-2 {
    grid-template-columns: repeat(2, 1fr);
}

@media (max-width: 640px) {
    .form-grid,
    .form-grid-2 {
        grid-template-columns: 1fr;
    }
}

/* === TOGGLE SWITCH === */
.toggle-switch {
    position: relative;
    display: inline-block;
    width: 48px;
    height: 24px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--vendor-border-primary);
    transition: var(--vendor-transition-fast);
    border-radius: 24px;
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background: var(--vendor-white);
    transition: var(--vendor-transition-fast);
    border-radius: 50%;
}

input:checked + .toggle-slider {
    background: var(--vendor-brand-primary);
}

input:checked + .toggle-slider:before {
    transform: translateX(24px);
}

.toggle-group {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--vendor-space-4);
    border: 1px solid var(--vendor-border-primary);
    border-radius: var(--vendor-radius-md);
    background: var(--vendor-surface-secondary);
}

.toggle-info {
    flex: 1;
    margin-right: var(--vendor-space-4);
}

.toggle-title {
    font-size: var(--vendor-text-sm);
    font-weight: var(--vendor-font-medium);
    color: var(--vendor-text-primary);
    margin-bottom: var(--vendor-space-1);
}

.toggle-description {
    font-size: var(--vendor-text-xs);
    color: var(--vendor-text-secondary);
}

/* === FILE UPLOAD === */
.file-upload {
    position: relative;
    display: inline-block;
    cursor: pointer;
}

.file-upload-input {
    position: absolute;
    opacity: 0;
    width: 0;
    height: 0;
}

.file-upload-label {
    display: flex;
    align-items: center;
    gap: var(--vendor-space-2);
    padding: var(--vendor-space-3) var(--vendor-space-4);
    border: 2px dashed var(--vendor-border-primary);
    border-radius: var(--vendor-radius-md);
    background: var(--vendor-surface-secondary);
    color: var(--vendor-text-secondary);
    cursor: pointer;
    transition: var(--vendor-transition-fast);
    text-align: center;
    min-height: 120px;
    flex-direction: column;
    justify-content: center;
}

.file-upload-label:hover {
    border-color: var(--vendor-brand-primary);
    background: var(--vendor-brand-primary-50);
    color: var(--vendor-brand-primary);
}

.file-upload-icon {
    font-size: var(--vendor-text-2xl);
    margin-bottom: var(--vendor-space-2);
}

.file-upload-text {
    font-size: var(--vendor-text-sm);
    font-weight: var(--vendor-font-medium);
}

.file-upload-hint {
    font-size: var(--vendor-text-xs);
    color: var(--vendor-text-tertiary);
    margin-top: var(--vendor-space-1);
}

/* === SETTINGS ACTIONS === */
.settings-actions {
    display: flex;
    gap: var(--vendor-space-3);
    padding: var(--vendor-space-5);
    border-top: 1px solid var(--vendor-border-primary);
    background: var(--vendor-surface-secondary);
}

@media (max-width: 640px) {
    .settings-actions {
        flex-direction: column;
    }
}

/* === DANGER ZONE === */
.danger-zone {
    margin-top: var(--vendor-space-8);
    padding: var(--vendor-space-5);
    border: 1px solid var(--vendor-error);
    border-radius: var(--vendor-radius-md);
    background: var(--vendor-error-50);
}

.danger-zone-title {
    font-size: var(--vendor-text-lg);
    font-weight: var(--vendor-font-semibold);
    color: var(--vendor-error);
    margin-bottom: var(--vendor-space-3);
}

.danger-zone-description {
    font-size: var(--vendor-text-sm);
    color: var(--vendor-error-700);
    margin-bottom: var(--vendor-space-4);
}

.danger-zone-actions {
    display: flex;
    gap: var(--vendor-space-3);
}

@media (max-width: 640px) {
    .danger-zone-actions {
        flex-direction: column;
    }
}
