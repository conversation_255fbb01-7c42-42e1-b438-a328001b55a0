/* ===== VENDOR PRODUCTS PAGE CSS ===== */

/* === PAGE HEADER === */
.products-header {
    background: var(--vendor-surface-primary);
    border: 1px solid var(--vendor-border-primary);
    border-radius: var(--vendor-radius-xl);
    padding: var(--vendor-space-6);
    margin-bottom: var(--vendor-space-6);
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: var(--vendor-space-4);
}

@media (max-width: 768px) {
    .products-header {
        flex-direction: column;
        align-items: stretch;
        gap: var(--vendor-space-4);
    }
}

.products-header-left {
    flex: 1;
}

.products-title {
    font-size: var(--vendor-text-2xl);
    font-weight: var(--vendor-font-bold);
    color: var(--vendor-text-primary);
    margin-bottom: var(--vendor-space-2);
}

.products-subtitle {
    font-size: var(--vendor-text-base);
    color: var(--vendor-text-secondary);
}

.products-header-right {
    display: flex;
    gap: var(--vendor-space-3);
}

@media (max-width: 640px) {
    .products-header-right {
        flex-direction: column;
    }
}

/* === FILTERS & SEARCH === */
.products-filters {
    background: var(--vendor-surface-primary);
    border: 1px solid var(--vendor-border-primary);
    border-radius: var(--vendor-radius-lg);
    padding: var(--vendor-space-6);
    margin-bottom: var(--vendor-space-6);
}

.filters-row {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr auto;
    gap: var(--vendor-space-4);
    align-items: end;
}

@media (max-width: 1024px) {
    .filters-row {
        grid-template-columns: 1fr 1fr;
        gap: var(--vendor-space-3);
    }
}

@media (max-width: 640px) {
    .filters-row {
        grid-template-columns: 1fr;
        gap: var(--vendor-space-3);
    }
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: var(--vendor-space-2);
}

.filter-label {
    font-size: var(--vendor-text-sm);
    font-weight: var(--vendor-font-medium);
    color: var(--vendor-text-primary);
}

.filter-input,
.filter-select {
    padding: var(--vendor-space-3);
    border: 1px solid var(--vendor-border-primary);
    border-radius: var(--vendor-radius-md);
    background: var(--vendor-surface-secondary);
    color: var(--vendor-text-primary);
    font-size: var(--vendor-text-sm);
    transition: var(--vendor-transition-fast);
}

.filter-input:focus,
.filter-select:focus {
    outline: none;
    border-color: var(--vendor-brand-primary);
    box-shadow: 0 0 0 3px rgba(126, 217, 87, 0.1);
}

.search-input-container {
    position: relative;
}

.search-input {
    padding-left: var(--vendor-space-10);
}

.search-icon {
    position: absolute;
    left: var(--vendor-space-3);
    top: 50%;
    transform: translateY(-50%);
    color: var(--vendor-text-tertiary);
    font-size: var(--vendor-text-base);
}

.filter-actions {
    display: flex;
    gap: var(--vendor-space-2);
}

/* === PRODUCTS GRID === */
.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: var(--vendor-space-6);
    margin-bottom: var(--vendor-space-8);
}

@media (max-width: 640px) {
    .products-grid {
        grid-template-columns: 1fr;
        gap: var(--vendor-space-4);
    }
}

.product-card {
    background: var(--vendor-surface-primary);
    border: 1px solid var(--vendor-border-primary);
    border-radius: var(--vendor-radius-xl);
    overflow: hidden;
    transition: var(--vendor-transition-fast);
    position: relative;
}

.product-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--vendor-shadow-lg);
    border-color: var(--vendor-brand-primary);
}

.product-image-container {
    position: relative;
    height: 200px;
    background: var(--vendor-surface-secondary);
    overflow: hidden;
}

.product-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--vendor-transition-fast);
}

.product-card:hover .product-image {
    transform: scale(1.05);
}

.product-image-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--vendor-text-tertiary);
    font-size: var(--vendor-text-4xl);
}

.product-status-badge {
    position: absolute;
    top: var(--vendor-space-3);
    left: var(--vendor-space-3);
    padding: var(--vendor-space-1) var(--vendor-space-3);
    border-radius: var(--vendor-radius-full);
    font-size: var(--vendor-text-xs);
    font-weight: var(--vendor-font-semibold);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.product-status-badge.active {
    background: var(--vendor-success-100);
    color: var(--vendor-success-700);
}

.product-status-badge.inactive {
    background: var(--vendor-error-100);
    color: var(--vendor-error-700);
}

.product-status-badge.draft {
    background: var(--vendor-warning-100);
    color: var(--vendor-warning-700);
}

.product-actions-overlay {
    position: absolute;
    top: var(--vendor-space-3);
    right: var(--vendor-space-3);
    display: flex;
    gap: var(--vendor-space-2);
    opacity: 0;
    transition: var(--vendor-transition-fast);
}

.product-card:hover .product-actions-overlay {
    opacity: 1;
}

.product-action-btn {
    width: 32px;
    height: 32px;
    background: rgba(255, 255, 255, 0.9);
    border: none;
    border-radius: var(--vendor-radius-full);
    color: var(--vendor-text-secondary);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--vendor-transition-fast);
    backdrop-filter: blur(10px);
}

.product-action-btn:hover {
    background: var(--vendor-white);
    color: var(--vendor-brand-primary);
    transform: scale(1.1);
}

.product-content {
    padding: var(--vendor-space-5);
}

.product-category {
    font-size: var(--vendor-text-xs);
    color: var(--vendor-brand-primary);
    font-weight: var(--vendor-font-semibold);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: var(--vendor-space-2);
}

.product-name {
    font-size: var(--vendor-text-lg);
    font-weight: var(--vendor-font-semibold);
    color: var(--vendor-text-primary);
    margin-bottom: var(--vendor-space-2);
    line-height: var(--vendor-leading-tight);
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.product-description {
    font-size: var(--vendor-text-sm);
    color: var(--vendor-text-secondary);
    margin-bottom: var(--vendor-space-4);
    line-height: var(--vendor-leading-relaxed);
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.product-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--vendor-space-4);
}

.product-price {
    font-size: var(--vendor-text-xl);
    font-weight: var(--vendor-font-bold);
    color: var(--vendor-brand-primary);
}

.product-stock {
    font-size: var(--vendor-text-sm);
    color: var(--vendor-text-tertiary);
}

.product-stock.low {
    color: var(--vendor-warning);
    font-weight: var(--vendor-font-medium);
}

.product-stock.out {
    color: var(--vendor-error);
    font-weight: var(--vendor-font-medium);
}

.product-footer {
    display: flex;
    gap: var(--vendor-space-2);
}

.product-btn {
    flex: 1;
    padding: var(--vendor-space-2) var(--vendor-space-4);
    border: 1px solid var(--vendor-border-primary);
    border-radius: var(--vendor-radius-md);
    background: var(--vendor-surface-secondary);
    color: var(--vendor-text-secondary);
    font-size: var(--vendor-text-sm);
    font-weight: var(--vendor-font-medium);
    cursor: pointer;
    transition: var(--vendor-transition-fast);
    text-decoration: none;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--vendor-space-2);
}

.product-btn:hover {
    background: var(--vendor-brand-primary);
    color: var(--vendor-white);
    border-color: var(--vendor-brand-primary);
    transform: translateY(-1px);
}

.product-btn.primary {
    background: var(--vendor-brand-primary);
    color: var(--vendor-white);
    border-color: var(--vendor-brand-primary);
}

.product-btn.primary:hover {
    background: var(--vendor-brand-primary-600);
    border-color: var(--vendor-brand-primary-600);
}

/* === EMPTY STATE === */
.products-empty {
    text-align: center;
    padding: var(--vendor-space-16) var(--vendor-space-8);
    background: var(--vendor-surface-primary);
    border: 1px solid var(--vendor-border-primary);
    border-radius: var(--vendor-radius-xl);
}

.empty-icon {
    width: 80px;
    height: 80px;
    background: var(--vendor-surface-secondary);
    border-radius: var(--vendor-radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--vendor-space-6);
    color: var(--vendor-text-tertiary);
    font-size: var(--vendor-text-3xl);
}

.empty-title {
    font-size: var(--vendor-text-xl);
    font-weight: var(--vendor-font-semibold);
    color: var(--vendor-text-primary);
    margin-bottom: var(--vendor-space-3);
}

.empty-description {
    font-size: var(--vendor-text-base);
    color: var(--vendor-text-secondary);
    margin-bottom: var(--vendor-space-6);
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
}

/* === PAGINATION === */
.products-pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: var(--vendor-space-2);
    margin-top: var(--vendor-space-8);
}

.pagination-btn {
    padding: var(--vendor-space-2) var(--vendor-space-4);
    border: 1px solid var(--vendor-border-primary);
    border-radius: var(--vendor-radius-md);
    background: var(--vendor-surface-primary);
    color: var(--vendor-text-secondary);
    font-size: var(--vendor-text-sm);
    cursor: pointer;
    transition: var(--vendor-transition-fast);
    text-decoration: none;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 40px;
}

.pagination-btn:hover:not(.disabled) {
    background: var(--vendor-brand-primary);
    color: var(--vendor-white);
    border-color: var(--vendor-brand-primary);
}

.pagination-btn.active {
    background: var(--vendor-brand-primary);
    color: var(--vendor-white);
    border-color: var(--vendor-brand-primary);
}

.pagination-btn.disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pagination-info {
    font-size: var(--vendor-text-sm);
    color: var(--vendor-text-tertiary);
    margin: 0 var(--vendor-space-4);
}
