/* ===== MODERN VENDOR UTILITIES CSS ===== */

/* === SPACING UTILITIES === */
.p-0 { padding: var(--vendor-space-0) !important; }
.p-1 { padding: var(--vendor-space-1) !important; }
.p-2 { padding: var(--vendor-space-2) !important; }
.p-3 { padding: var(--vendor-space-3) !important; }
.p-4 { padding: var(--vendor-space-4) !important; }
.p-5 { padding: var(--vendor-space-5) !important; }
.p-6 { padding: var(--vendor-space-6) !important; }
.p-8 { padding: var(--vendor-space-8) !important; }
.p-10 { padding: var(--vendor-space-10) !important; }
.p-12 { padding: var(--vendor-space-12) !important; }

.px-0 { padding-left: var(--vendor-space-0) !important; padding-right: var(--vendor-space-0) !important; }
.px-1 { padding-left: var(--vendor-space-1) !important; padding-right: var(--vendor-space-1) !important; }
.px-2 { padding-left: var(--vendor-space-2) !important; padding-right: var(--vendor-space-2) !important; }
.px-3 { padding-left: var(--vendor-space-3) !important; padding-right: var(--vendor-space-3) !important; }
.px-4 { padding-left: var(--vendor-space-4) !important; padding-right: var(--vendor-space-4) !important; }
.px-5 { padding-left: var(--vendor-space-5) !important; padding-right: var(--vendor-space-5) !important; }
.px-6 { padding-left: var(--vendor-space-6) !important; padding-right: var(--vendor-space-6) !important; }
.px-8 { padding-left: var(--vendor-space-8) !important; padding-right: var(--vendor-space-8) !important; }

.py-0 { padding-top: var(--vendor-space-0) !important; padding-bottom: var(--vendor-space-0) !important; }
.py-1 { padding-top: var(--vendor-space-1) !important; padding-bottom: var(--vendor-space-1) !important; }
.py-2 { padding-top: var(--vendor-space-2) !important; padding-bottom: var(--vendor-space-2) !important; }
.py-3 { padding-top: var(--vendor-space-3) !important; padding-bottom: var(--vendor-space-3) !important; }
.py-4 { padding-top: var(--vendor-space-4) !important; padding-bottom: var(--vendor-space-4) !important; }
.py-5 { padding-top: var(--vendor-space-5) !important; padding-bottom: var(--vendor-space-5) !important; }
.py-6 { padding-top: var(--vendor-space-6) !important; padding-bottom: var(--vendor-space-6) !important; }
.py-8 { padding-top: var(--vendor-space-8) !important; padding-bottom: var(--vendor-space-8) !important; }

.m-0 { margin: var(--vendor-space-0) !important; }
.m-1 { margin: var(--vendor-space-1) !important; }
.m-2 { margin: var(--vendor-space-2) !important; }
.m-3 { margin: var(--vendor-space-3) !important; }
.m-4 { margin: var(--vendor-space-4) !important; }
.m-5 { margin: var(--vendor-space-5) !important; }
.m-6 { margin: var(--vendor-space-6) !important; }
.m-8 { margin: var(--vendor-space-8) !important; }
.m-10 { margin: var(--vendor-space-10) !important; }
.m-12 { margin: var(--vendor-space-12) !important; }

.mx-0 { margin-left: var(--vendor-space-0) !important; margin-right: var(--vendor-space-0) !important; }
.mx-1 { margin-left: var(--vendor-space-1) !important; margin-right: var(--vendor-space-1) !important; }
.mx-2 { margin-left: var(--vendor-space-2) !important; margin-right: var(--vendor-space-2) !important; }
.mx-3 { margin-left: var(--vendor-space-3) !important; margin-right: var(--vendor-space-3) !important; }
.mx-4 { margin-left: var(--vendor-space-4) !important; margin-right: var(--vendor-space-4) !important; }
.mx-auto { margin-left: auto !important; margin-right: auto !important; }

.my-0 { margin-top: var(--vendor-space-0) !important; margin-bottom: var(--vendor-space-0) !important; }
.my-1 { margin-top: var(--vendor-space-1) !important; margin-bottom: var(--vendor-space-1) !important; }
.my-2 { margin-top: var(--vendor-space-2) !important; margin-bottom: var(--vendor-space-2) !important; }
.my-3 { margin-top: var(--vendor-space-3) !important; margin-bottom: var(--vendor-space-3) !important; }
.my-4 { margin-top: var(--vendor-space-4) !important; margin-bottom: var(--vendor-space-4) !important; }
.my-5 { margin-top: var(--vendor-space-5) !important; margin-bottom: var(--vendor-space-5) !important; }
.my-6 { margin-top: var(--vendor-space-6) !important; margin-bottom: var(--vendor-space-6) !important; }
.my-8 { margin-top: var(--vendor-space-8) !important; margin-bottom: var(--vendor-space-8) !important; }

/* === DISPLAY UTILITIES === */
.d-none { display: none !important; }
.d-block { display: block !important; }
.d-inline { display: inline !important; }
.d-inline-block { display: inline-block !important; }
.d-flex { display: flex !important; }
.d-inline-flex { display: inline-flex !important; }
.d-grid { display: grid !important; }

/* === FLEXBOX UTILITIES === */
.flex-row { flex-direction: row !important; }
.flex-col { flex-direction: column !important; }
.flex-wrap { flex-wrap: wrap !important; }
.flex-nowrap { flex-wrap: nowrap !important; }

.justify-start { justify-content: flex-start !important; }
.justify-center { justify-content: center !important; }
.justify-end { justify-content: flex-end !important; }
.justify-between { justify-content: space-between !important; }
.justify-around { justify-content: space-around !important; }
.justify-evenly { justify-content: space-evenly !important; }

.items-start { align-items: flex-start !important; }
.items-center { align-items: center !important; }
.items-end { align-items: flex-end !important; }
.items-stretch { align-items: stretch !important; }
.items-baseline { align-items: baseline !important; }

.flex-1 { flex: 1 1 0% !important; }
.flex-auto { flex: 1 1 auto !important; }
.flex-initial { flex: 0 1 auto !important; }
.flex-none { flex: none !important; }

/* === GRID UTILITIES === */
.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)) !important; }
.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)) !important; }
.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)) !important; }
.grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)) !important; }
.grid-cols-5 { grid-template-columns: repeat(5, minmax(0, 1fr)) !important; }
.grid-cols-6 { grid-template-columns: repeat(6, minmax(0, 1fr)) !important; }
.grid-cols-12 { grid-template-columns: repeat(12, minmax(0, 1fr)) !important; }

.gap-0 { gap: var(--vendor-space-0) !important; }
.gap-1 { gap: var(--vendor-space-1) !important; }
.gap-2 { gap: var(--vendor-space-2) !important; }
.gap-3 { gap: var(--vendor-space-3) !important; }
.gap-4 { gap: var(--vendor-space-4) !important; }
.gap-5 { gap: var(--vendor-space-5) !important; }
.gap-6 { gap: var(--vendor-space-6) !important; }
.gap-8 { gap: var(--vendor-space-8) !important; }

/* === TEXT UTILITIES === */
.text-left { text-align: left !important; }
.text-center { text-align: center !important; }
.text-right { text-align: right !important; }
.text-justify { text-align: justify !important; }

.text-xs { font-size: var(--vendor-text-xs) !important; }
.text-sm { font-size: var(--vendor-text-sm) !important; }
.text-base { font-size: var(--vendor-text-base) !important; }
.text-lg { font-size: var(--vendor-text-lg) !important; }
.text-xl { font-size: var(--vendor-text-xl) !important; }
.text-2xl { font-size: var(--vendor-text-2xl) !important; }
.text-3xl { font-size: var(--vendor-text-3xl) !important; }

.font-thin { font-weight: var(--vendor-font-thin) !important; }
.font-light { font-weight: var(--vendor-font-light) !important; }
.font-normal { font-weight: var(--vendor-font-normal) !important; }
.font-medium { font-weight: var(--vendor-font-medium) !important; }
.font-semibold { font-weight: var(--vendor-font-semibold) !important; }
.font-bold { font-weight: var(--vendor-font-bold) !important; }
.font-extrabold { font-weight: var(--vendor-font-extrabold) !important; }

.text-primary { color: var(--vendor-text-primary) !important; }
.text-secondary { color: var(--vendor-text-secondary) !important; }
.text-tertiary { color: var(--vendor-text-tertiary) !important; }
.text-brand { color: var(--vendor-brand-primary) !important; }
.text-success { color: var(--vendor-success) !important; }
.text-warning { color: var(--vendor-warning) !important; }
.text-error { color: var(--vendor-error) !important; }
.text-info { color: var(--vendor-info) !important; }
.text-white { color: var(--vendor-white) !important; }

/* === BACKGROUND UTILITIES === */
.bg-primary { background-color: var(--vendor-bg-primary) !important; }
.bg-secondary { background-color: var(--vendor-bg-secondary) !important; }
.bg-tertiary { background-color: var(--vendor-bg-tertiary) !important; }
.bg-surface { background-color: var(--vendor-surface-primary) !important; }
.bg-brand { background-color: var(--vendor-brand-primary) !important; }
.bg-success { background-color: var(--vendor-success) !important; }
.bg-warning { background-color: var(--vendor-warning) !important; }
.bg-error { background-color: var(--vendor-error) !important; }
.bg-info { background-color: var(--vendor-info) !important; }
.bg-white { background-color: var(--vendor-white) !important; }
.bg-transparent { background-color: transparent !important; }

/* === BORDER UTILITIES === */
.border { border: 1px solid var(--vendor-border-primary) !important; }
.border-0 { border: 0 !important; }
.border-t { border-top: 1px solid var(--vendor-border-primary) !important; }
.border-r { border-right: 1px solid var(--vendor-border-primary) !important; }
.border-b { border-bottom: 1px solid var(--vendor-border-primary) !important; }
.border-l { border-left: 1px solid var(--vendor-border-primary) !important; }

.border-primary { border-color: var(--vendor-border-primary) !important; }
.border-secondary { border-color: var(--vendor-border-secondary) !important; }
.border-brand { border-color: var(--vendor-brand-primary) !important; }
.border-success { border-color: var(--vendor-success) !important; }
.border-warning { border-color: var(--vendor-warning) !important; }
.border-error { border-color: var(--vendor-error) !important; }

.rounded-none { border-radius: var(--vendor-radius-none) !important; }
.rounded-sm { border-radius: var(--vendor-radius-sm) !important; }
.rounded { border-radius: var(--vendor-radius-base) !important; }
.rounded-md { border-radius: var(--vendor-radius-md) !important; }
.rounded-lg { border-radius: var(--vendor-radius-lg) !important; }
.rounded-xl { border-radius: var(--vendor-radius-xl) !important; }
.rounded-2xl { border-radius: var(--vendor-radius-2xl) !important; }
.rounded-3xl { border-radius: var(--vendor-radius-3xl) !important; }
.rounded-full { border-radius: var(--vendor-radius-full) !important; }

/* === SHADOW UTILITIES === */
.shadow-none { box-shadow: var(--vendor-shadow-none) !important; }
.shadow-sm { box-shadow: var(--vendor-shadow-sm) !important; }
.shadow { box-shadow: var(--vendor-shadow-base) !important; }
.shadow-md { box-shadow: var(--vendor-shadow-md) !important; }
.shadow-lg { box-shadow: var(--vendor-shadow-lg) !important; }
.shadow-xl { box-shadow: var(--vendor-shadow-xl) !important; }
.shadow-2xl { box-shadow: var(--vendor-shadow-2xl) !important; }
.shadow-brand { box-shadow: var(--vendor-shadow-brand) !important; }
.shadow-brand-lg { box-shadow: var(--vendor-shadow-brand-lg) !important; }

/* === POSITION UTILITIES === */
.relative { position: relative !important; }
.absolute { position: absolute !important; }
.fixed { position: fixed !important; }
.sticky { position: sticky !important; }
.static { position: static !important; }

.top-0 { top: 0 !important; }
.right-0 { right: 0 !important; }
.bottom-0 { bottom: 0 !important; }
.left-0 { left: 0 !important; }

/* === OVERFLOW UTILITIES === */
.overflow-auto { overflow: auto !important; }
.overflow-hidden { overflow: hidden !important; }
.overflow-visible { overflow: visible !important; }
.overflow-scroll { overflow: scroll !important; }

.overflow-x-auto { overflow-x: auto !important; }
.overflow-x-hidden { overflow-x: hidden !important; }
.overflow-x-scroll { overflow-x: scroll !important; }

.overflow-y-auto { overflow-y: auto !important; }
.overflow-y-hidden { overflow-y: hidden !important; }
.overflow-y-scroll { overflow-y: scroll !important; }

/* === TRANSITION UTILITIES === */
.transition { transition: var(--vendor-transition-fast) !important; }
.transition-all { transition: var(--vendor-transition-base) !important; }
.transition-slow { transition: var(--vendor-transition-slow) !important; }
.transition-none { transition: none !important; }

/* === TRANSFORM UTILITIES === */
.rotate-0 { transform: rotate(0deg) !important; }
.rotate-45 { transform: rotate(45deg) !important; }
.rotate-90 { transform: rotate(90deg) !important; }
.rotate-180 { transform: rotate(180deg) !important; }
.-rotate-45 { transform: rotate(-45deg) !important; }
.-rotate-90 { transform: rotate(-90deg) !important; }
.-rotate-180 { transform: rotate(-180deg) !important; }

/* === RESPONSIVE UTILITIES === */
@media (max-width: 640px) {
    .sm\:d-none { display: none !important; }
    .sm\:d-block { display: block !important; }
    .sm\:d-flex { display: flex !important; }
    .sm\:text-center { text-align: center !important; }
    .sm\:p-4 { padding: var(--vendor-space-4) !important; }
    .sm\:px-4 { padding-left: var(--vendor-space-4) !important; padding-right: var(--vendor-space-4) !important; }
    .sm\:py-4 { padding-top: var(--vendor-space-4) !important; padding-bottom: var(--vendor-space-4) !important; }
}

@media (max-width: 768px) {
    .md\:d-none { display: none !important; }
    .md\:d-block { display: block !important; }
    .md\:d-flex { display: flex !important; }
    .md\:text-center { text-align: center !important; }
    .md\:p-6 { padding: var(--vendor-space-6) !important; }
    .md\:px-6 { padding-left: var(--vendor-space-6) !important; padding-right: var(--vendor-space-6) !important; }
    .md\:py-6 { padding-top: var(--vendor-space-6) !important; padding-bottom: var(--vendor-space-6) !important; }
}

@media (max-width: 1024px) {
    .lg\:d-none { display: none !important; }
    .lg\:d-block { display: block !important; }
    .lg\:d-flex { display: flex !important; }
    .lg\:text-center { text-align: center !important; }
    .lg\:p-8 { padding: var(--vendor-space-8) !important; }
    .lg\:px-8 { padding-left: var(--vendor-space-8) !important; padding-right: var(--vendor-space-8) !important; }
    .lg\:py-8 { padding-top: var(--vendor-space-8) !important; padding-bottom: var(--vendor-space-8) !important; }
}
