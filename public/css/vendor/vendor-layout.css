/* ===== VENDOR LAYOUT CSS ===== */

/* === MAIN LAYOUT STRUCTURE === */
.vendor-body {
    font-family: 'Inter', 'Poppins', Arial, sans-serif;
    background: #f9fafb;
    color: #1f2937;
    margin: 0;
    padding: 0;
    min-height: 100vh;
    overflow-x: hidden;
    line-height: 1.6;
}

/* Loading Screen */
.vendor-loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, var(--vendor-brand-primary), var(--vendor-brand-primary-700));
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: var(--vendor-z-modal);
    backdrop-filter: blur(10px);
}

.loading-content {
    text-align: center;
    color: var(--vendor-white);
    animation: fadeInUp 0.6s ease-out;
}

.loading-logo {
    margin-bottom: var(--vendor-space-8);
    animation: pulse 2s infinite;
}

.loading-logo img {
    height: 60px;
    width: auto;
    filter: brightness(0) invert(1);
}

.loading-logo-fallback {
    width: 60px;
    height: 60px;
    background: var(--vendor-white);
    color: var(--vendor-brand-primary);
    border-radius: var(--vendor-radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--vendor-text-2xl);
    font-weight: var(--vendor-font-bold);
    margin: 0 auto;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-top: 3px solid var(--vendor-white);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto var(--vendor-space-4);
}

.loading-text {
    font-size: var(--vendor-text-lg);
    font-weight: var(--vendor-font-medium);
    opacity: 0.9;
}

/* Main App Container */
.vendor-app {
    display: flex;
    min-height: 100vh;
    background: #f9fafb;
    position: relative;
    overflow-x: hidden;
}

/* Mobile Overlay */
.vendor-mobile-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1040;
    backdrop-filter: blur(4px);
    display: none;
}

@media (max-width: 1024px) {
    .vendor-mobile-overlay {
        display: block;
    }
}

/* === SIDEBAR === */
.vendor-sidebar {
    width: 280px;
    background: #ffffff;
    border-right: 1px solid #e5e7eb;
    display: flex;
    flex-direction: column;
    transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 1000;
    min-height: 100vh;
    position: fixed;
    top: 0;
    left: 0;
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
}

.vendor-sidebar.collapsed {
    width: 80px;
}

.vendor-sidebar.collapsed .nav-item-text,
.vendor-sidebar.collapsed .brand-text,
.vendor-sidebar.collapsed .nav-section-title {
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.2s ease, visibility 0.2s ease;
}

.vendor-sidebar:not(.collapsed) .nav-item-text,
.vendor-sidebar:not(.collapsed) .brand-text,
.vendor-sidebar:not(.collapsed) .nav-section-title {
    opacity: 1;
    visibility: visible;
    transition: opacity 0.3s ease 0.1s, visibility 0.3s ease 0.1s;
}

/* Mobile Sidebar */
@media (max-width: 1024px) {
    .vendor-sidebar {
        position: fixed;
        top: 0;
        left: 0;
        height: 100vh;
        transform: translateX(-100%);
        z-index: var(--vendor-z-modal);
        box-shadow: var(--vendor-shadow-xl);
    }
    
    .vendor-sidebar.mobile-open {
        transform: translateX(0);
    }
}

/* Sidebar Header */
.sidebar-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 24px 20px;
    border-bottom: 1px solid #e5e7eb;
    background: #ffffff;
    min-height: 80px;
}

.sidebar-brand {
    display: flex;
    align-items: center;
    gap: 12px;
    text-decoration: none;
    color: #1f2937;
    transition: all 0.2s ease;
    cursor: pointer;
}

.sidebar-brand:hover {
    transform: translateY(-1px);
}

.brand-logo {
    position: relative;
}

.brand-logo-img {
    height: 40px;
    width: auto;
    border-radius: 8px;
    transition: all 0.2s ease;
}

.brand-logo-fallback {
    width: 40px;
    height: 40px;
    background: #7ED957;
    color: #ffffff;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    font-weight: 600;
}

.brand-text {
    transition: var(--vendor-transition-fast);
}

.brand-title {
    font-size: 20px;
    font-weight: 700;
    color: #7ED957;
    white-space: nowrap;
    margin: 0;
    line-height: 1.2;
}

.brand-subtitle {
    font-size: 12px;
    color: #6b7280;
    margin: 0;
    line-height: 1;
    font-weight: 500;
}

.vendor-sidebar.collapsed .brand-text {
    opacity: 0;
    width: 0;
    overflow: hidden;
}

.sidebar-controls {
    display: flex;
    gap: var(--vendor-space-2);
}

.sidebar-toggle-btn,
.sidebar-close-btn {
    background: none;
    border: none;
    color: var(--vendor-text-secondary);
    font-size: var(--vendor-text-lg);
    cursor: pointer;
    padding: var(--vendor-space-2);
    border-radius: var(--vendor-radius-base);
    transition: var(--vendor-transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
}

.sidebar-toggle-btn:hover,
.sidebar-close-btn:hover {
    background: var(--vendor-surface-hover);
    color: var(--vendor-text-primary);
}

.desktop-only {
    display: flex;
}

.mobile-only {
    display: none;
}

@media (max-width: 1024px) {
    .desktop-only {
        display: none;
    }

    .mobile-only {
        display: flex;
    }
}

.sidebar-toggle {
    background: none;
    border: none;
    color: var(--vendor-text-secondary);
    font-size: var(--vendor-text-lg);
    cursor: pointer;
    padding: var(--vendor-space-2);
    border-radius: var(--vendor-radius-base);
    transition: var(--vendor-transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
}

.sidebar-toggle:hover {
    background: var(--vendor-surface-hover);
    color: var(--vendor-text-primary);
}

/* Sidebar Navigation */
.sidebar-nav {
    flex: 1;
    padding: var(--vendor-space-4) 0;
    overflow-y: auto;
    overflow-x: hidden;
}

.nav-section {
    margin-bottom: var(--vendor-space-6);
}

.nav-section-title {
    font-size: var(--vendor-text-xs);
    font-weight: var(--vendor-font-semibold);
    color: var(--vendor-text-tertiary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    padding: 0 var(--vendor-space-5) var(--vendor-space-2);
    margin-bottom: var(--vendor-space-2);
    transition: var(--vendor-transition-fast);
}

.vendor-sidebar.collapsed .nav-section-title {
    opacity: 0;
    height: 0;
    padding: 0;
    margin: 0;
    overflow: hidden;
}

.nav-items {
    display: flex;
    flex-direction: column;
    gap: var(--vendor-space-1);
}

.nav-item {
    display: flex;
    align-items: center;
    gap: 12px;
    color: #6b7280;
    text-decoration: none;
    padding: 12px 20px;
    margin: 0 12px;
    border-radius: 8px;
    transition: all 0.2s ease;
    position: relative;
    font-weight: 500;
    font-size: 14px;
}

.nav-item:hover {
    background: #f3f4f6;
    color: #1f2937;
    transform: translateX(2px);
}

.nav-item.active {
    background: rgba(126, 217, 87, 0.1);
    color: #7ED957;
    font-weight: 600;
}

.nav-item.active::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 3px;
    height: 20px;
    background: #7ED957;
    border-radius: 0 4px 4px 0;
}

.nav-item-indicator {
    position: absolute;
    right: var(--vendor-space-3);
    width: 6px;
    height: 6px;
    background: var(--vendor-brand-primary);
    border-radius: 50%;
    opacity: 0;
    transition: var(--vendor-transition-fast);
}

.nav-item.active .nav-item-indicator {
    opacity: 1;
}

.nav-item-icon {
    font-size: var(--vendor-text-lg);
    width: 20px;
    text-align: center;
    flex-shrink: 0;
}

.nav-item-text {
    white-space: nowrap;
    transition: var(--vendor-transition-fast);
}

.vendor-sidebar.collapsed .nav-item-text {
    opacity: 0;
    width: 0;
    overflow: hidden;
}

.nav-item-badge {
    background: var(--vendor-error);
    color: var(--vendor-white);
    font-size: var(--vendor-text-xs);
    font-weight: var(--vendor-font-semibold);
    padding: 2px 6px;
    border-radius: var(--vendor-radius-full);
    margin-left: auto;
    min-width: 18px;
    text-align: center;
    transition: var(--vendor-transition-fast);
}

.vendor-sidebar.collapsed .nav-item-badge {
    opacity: 0;
    width: 0;
    overflow: hidden;
}

/* Sidebar Footer */
.sidebar-footer {
    padding: var(--vendor-space-4);
    border-top: 1px solid var(--vendor-border-primary);
    background: var(--vendor-surface-secondary);
}

.sidebar-user {
    display: flex;
    align-items: center;
    gap: var(--vendor-space-3);
    padding: var(--vendor-space-3);
    border-radius: var(--vendor-radius-md);
    transition: var(--vendor-transition-fast);
    cursor: pointer;
}

.sidebar-user:hover {
    background: var(--vendor-surface-hover);
}

.sidebar-user-avatar {
    width: 36px;
    height: 36px;
    border-radius: var(--vendor-radius-full);
    background: var(--vendor-brand-primary);
    color: var(--vendor-white);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: var(--vendor-font-semibold);
    flex-shrink: 0;
}

.sidebar-user-info {
    flex: 1;
    min-width: 0;
    transition: var(--vendor-transition-fast);
}

.vendor-sidebar.collapsed .sidebar-user-info {
    opacity: 0;
    width: 0;
    overflow: hidden;
}

.sidebar-user-name {
    font-size: var(--vendor-text-sm);
    font-weight: var(--vendor-font-semibold);
    color: var(--vendor-text-primary);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.sidebar-user-role {
    font-size: var(--vendor-text-xs);
    color: var(--vendor-text-tertiary);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* === ANIMATIONS === */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

/* === MAIN CONTENT WRAPPER === */
.vendor-main-wrapper {
    flex: 1;
    display: flex;
    flex-direction: column;
    margin-left: 280px;
    transition: margin-left 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    min-height: 100vh;
    background: #f9fafb;
}

.vendor-sidebar.collapsed + .vendor-main-wrapper {
    margin-left: 80px;
}

/* === MAIN CONTENT AREA === */
.vendor-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: calc(100vh - 80px);
}

.page-content {
    flex: 1;
    padding: 32px;
    max-width: 1400px;
    margin: 0 auto;
    width: 100%;
}

/* === HEADER === */
.vendor-header {
    background: #ffffff;
    border-bottom: 1px solid #e5e7eb;
    padding: 0 32px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: sticky;
    top: 0;
    z-index: 100;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    margin: 0;
    width: 100%;
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    gap: 24px;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 16px;
    flex: 1;
}

.header-center {
    display: flex;
    align-items: center;
    justify-content: center;
    flex: 2;
    max-width: 600px;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 16px;
    flex: 1;
    justify-content: flex-end;
}

/* Search Container */
.search-container {
    width: 100%;
    max-width: 500px;
}

.search-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    background: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    padding: 0 16px;
    transition: all 0.2s ease;
}

.search-input-wrapper.focused {
    background: #ffffff;
    border-color: #6366f1;
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.search-icon {
    color: #9ca3af;
    margin-right: 12px;
    font-size: 16px;
}

.search-input {
    flex: 1;
    border: none;
    background: transparent;
    padding: 12px 0;
    font-size: 14px;
    color: #374151;
    outline: none;
}

.search-input::placeholder {
    color: #9ca3af;
}

.search-clear-btn {
    background: none;
    border: none;
    color: #9ca3af;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: color 0.2s ease;
}

.search-clear-btn:hover {
    color: #6b7280;
}

/* Mobile Menu Button */
.mobile-menu-btn {
    display: none;
    background: none;
    border: none;
    color: #374151;
    font-size: 20px;
    cursor: pointer;
    padding: 8px;
    border-radius: 8px;
    transition: all 0.2s ease;
}

.mobile-menu-btn:hover {
    background: #f3f4f6;
    color: #6366f1;
}

/* Page Title Section */
.page-title-section {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.page-title {
    font-size: 24px;
    font-weight: 700;
    color: #111827;
    margin: 0;
    line-height: 1.2;
}

.breadcrumb {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: #6b7280;
}

.breadcrumb-item {
    display: flex;
    align-items: center;
    gap: 6px;
}

.breadcrumb-item.current {
    color: #374151;
    font-weight: 500;
}

.breadcrumb-separator {
    color: #d1d5db;
    font-size: 12px;
}

/* === NOTIFICATION SYSTEM === */
.notifications-container {
    position: relative;
}

.notifications-btn {
    position: relative;
    background: none;
    border: none;
    color: #6b7280;
    font-size: 20px;
    cursor: pointer;
    padding: 12px;
    border-radius: 12px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.notifications-btn:hover {
    background: #f3f4f6;
    color: #374151;
}

.notification-badge {
    position: absolute;
    top: 8px;
    right: 8px;
    background: #ef4444;
    color: white;
    font-size: 11px;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: 1;
}

.notifications-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 16px;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    width: 380px;
    max-height: 500px;
    overflow: hidden;
    z-index: 1000;
    margin-top: 8px;
}

.notifications-header {
    padding: 20px 24px 16px;
    border-bottom: 1px solid #f3f4f6;
    background: #fafafa;
}

.notifications-title {
    font-size: 18px;
    font-weight: 600;
    color: #111827;
    margin: 0 0 8px 0;
}

.notifications-subtitle {
    font-size: 14px;
    color: #6b7280;
    margin: 0;
}

.notifications-list {
    max-height: 320px;
    overflow-y: auto;
}

.notification-item {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    padding: 16px 24px;
    border-bottom: 1px solid #f3f4f6;
    transition: background-color 0.2s ease;
    cursor: pointer;
}

.notification-item:hover {
    background: #f9fafb;
}

.notification-item:last-child {
    border-bottom: none;
}

.notification-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    color: white;
    flex-shrink: 0;
}

.notification-icon.order {
    background: #3b82f6;
}

.notification-icon.message {
    background: #10b981;
}

.notification-icon.success {
    background: #059669;
}

.notification-icon.warning {
    background: #f59e0b;
}

.notification-content {
    flex: 1;
    min-width: 0;
}

.notification-title {
    font-size: 14px;
    font-weight: 600;
    color: #111827;
    margin: 0 0 4px 0;
    line-height: 1.4;
}

.notification-message {
    font-size: 13px;
    color: #6b7280;
    margin: 0 0 6px 0;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.notification-time {
    font-size: 12px;
    color: #9ca3af;
    font-weight: 500;
}

.notifications-footer {
    padding: 16px 24px;
    border-top: 1px solid #f3f4f6;
    background: #fafafa;
    text-align: center;
}

.view-all-notifications {
    color: #6366f1;
    text-decoration: none;
    font-size: 14px;
    font-weight: 600;
    transition: color 0.2s ease;
}

.view-all-notifications:hover {
    color: #4f46e5;
}

/* === USER PROFILE === */
.user-profile-container {
    position: relative;
}

.user-profile-btn {
    display: flex;
    align-items: center;
    gap: 12px;
    background: none;
    border: none;
    cursor: pointer;
    padding: 8px 12px;
    border-radius: 12px;
    transition: all 0.2s ease;
    text-align: left;
}

.user-profile-btn:hover {
    background: #f3f4f6;
}

.user-avatar-wrapper {
    position: relative;
    flex-shrink: 0;
}

.user-avatar-img {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    object-fit: cover;
    border: 2px solid #e5e7eb;
}

.user-status-dot {
    position: absolute;
    bottom: 2px;
    right: 2px;
    width: 10px;
    height: 10px;
    background: #10b981;
    border: 2px solid white;
    border-radius: 50%;
}

.user-info {
    display: flex;
    flex-direction: column;
    gap: 2px;
    min-width: 0;
}

.user-name {
    font-size: 14px;
    font-weight: 600;
    color: #111827;
    line-height: 1.2;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.user-role {
    font-size: 12px;
    color: #6b7280;
    font-weight: 500;
    line-height: 1.2;
}

.user-dropdown-arrow {
    color: #9ca3af;
    font-size: 12px;
    transition: transform 0.2s ease;
}

.user-dropdown-arrow.rotate-180 {
    transform: rotate(180deg);
}

.user-profile-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 16px;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    width: 280px;
    overflow: hidden;
    z-index: 1000;
    margin-top: 8px;
}

.profile-dropdown-header {
    padding: 20px 24px;
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    color: white;
    display: flex;
    align-items: center;
    gap: 16px;
}

.profile-avatar img {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    border: 2px solid rgba(255, 255, 255, 0.2);
}

.profile-info {
    flex: 1;
    min-width: 0;
}

.profile-name {
    font-size: 16px;
    font-weight: 600;
    margin: 0 0 4px 0;
    line-height: 1.2;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.profile-email {
    font-size: 13px;
    color: rgba(255, 255, 255, 0.8);
    margin: 0 0 8px 0;
    line-height: 1.2;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.profile-badge {
    display: inline-block;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    font-size: 11px;
    font-weight: 600;
    padding: 4px 8px;
    border-radius: 6px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.profile-dropdown-menu {
    padding: 8px 0;
}

.profile-menu-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 24px;
    color: #374151;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s ease;
    border: none;
    background: none;
    width: 100%;
    text-align: left;
    cursor: pointer;
}

.profile-menu-item:hover {
    background: #f9fafb;
    color: #6366f1;
}

.profile-menu-item i {
    width: 16px;
    text-align: center;
    color: #9ca3af;
    transition: color 0.2s ease;
}

.profile-menu-item:hover i {
    color: #6366f1;
}

.profile-menu-divider {
    height: 1px;
    background: #f3f4f6;
    margin: 8px 0;
}

.profile-logout-form {
    margin: 0;
}

.logout-item {
    color: #dc2626 !important;
}

.logout-item:hover {
    background: #fef2f2 !important;
    color: #dc2626 !important;
}

.logout-item:hover i {
    color: #dc2626 !important;
}

@media (max-width: 1024px) {
    .page-content {
        padding: 16px;
    }

    .mobile-menu-btn {
        display: block;
    }

    .header-center {
        display: none;
    }

    .page-title {
        font-size: 20px;
    }

    .notifications-dropdown {
        width: 320px;
        right: -20px;
    }

    .user-profile-dropdown {
        width: 260px;
        right: -20px;
    }
}

@media (max-width: 768px) {
    .page-content {
        padding: 12px;
    }
}

/* === RESPONSIVE DESIGN IMPROVEMENTS === */

/* Mobile Sidebar Improvements */
@media (max-width: 1024px) {
    .vendor-sidebar {
        position: fixed;
        top: 0;
        left: 0;
        height: 100vh;
        transform: translateX(-100%);
        z-index: 1050;
        box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    }

    .vendor-sidebar.mobile-open {
        transform: translateX(0);
    }

    .vendor-main-wrapper {
        margin-left: 0;
    }
}

/* Mobile Header Improvements */
@media (max-width: 768px) {
    .vendor-header {
        height: 64px;
        padding: 0 16px;
    }

    .header-content {
        padding: 0 12px;
        gap: 12px;
    }

    .header-left {
        gap: 12px;
    }

    .header-right {
        gap: 8px;
    }

    .user-info {
        display: none;
    }

    .user-dropdown-arrow {
        display: none;
    }

    .page-title {
        font-size: 18px;
    }

    .breadcrumb {
        display: none;
    }

    .notifications-dropdown {
        width: calc(100vw - 32px);
        right: -16px;
        left: auto;
    }

    .user-profile-dropdown {
        width: calc(100vw - 32px);
        right: -16px;
        left: auto;
    }

    .sidebar-header {
        padding: 16px;
        min-height: 64px;
    }

    .brand-logo-img,
    .brand-logo-fallback {
        width: 32px;
        height: 32px;
    }

    .brand-title {
        font-size: 18px;
    }

    .brand-subtitle {
        font-size: 11px;
    }
}

/* Mobile Navigation Improvements */
@media (max-width: 640px) {
    .nav-item {
        padding: 10px 16px;
        margin: 0 8px;
        font-size: 13px;
    }

    .nav-section-title {
        padding: 0 16px 8px;
        font-size: 11px;
    }
}

/* Tablet Improvements */
@media (min-width: 768px) and (max-width: 1024px) {
    .vendor-sidebar {
        width: 260px;
    }

    .vendor-sidebar.collapsed {
        width: 70px;
    }
}
