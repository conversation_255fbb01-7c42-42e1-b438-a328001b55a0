/* Modern Vendor Dashboard - Enhanced Design System */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

:root {
  /* Color System */
  --primary: #10b981;
  --primary-dark: #059669;
  --primary-light: #d1fae5;
  --primary-50: #ecfdf5;
  --primary-100: #d1fae5;
  --primary-200: #a7f3d0;
  --primary-300: #6ee7b7;
  --primary-400: #34d399;
  --primary-500: #10b981;
  --primary-600: #059669;
  --primary-700: #047857;
  --primary-800: #065f46;
  --primary-900: #064e3b;
  
  /* Gray Scale */
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;
  
  /* Status Colors */
  --success: #10b981;
  --success-light: #d1fae5;
  --warning: #f59e0b;
  --warning-light: #fef3c7;
  --danger: #ef4444;
  --danger-light: #fee2e2;
  --info: #3b82f6;
  --info-light: #dbeafe;
  
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  
  /* Typography */
  --font-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  
  /* Spacing */
  --spacing-1: 0.25rem;
  --spacing-2: 0.5rem;
  --spacing-3: 0.75rem;
  --spacing-4: 1rem;
  --spacing-5: 1.25rem;
  --spacing-6: 1.5rem;
  --spacing-8: 2rem;
  --spacing-10: 2.5rem;
  --spacing-12: 3rem;
  --spacing-16: 4rem;
  --spacing-20: 5rem;
  --spacing-24: 6rem;
  --spacing-32: 8rem;
  
  /* Border Radius */
  --radius-sm: 0.25rem;
  --radius: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-full: 9999px;
  
  /* Transitions */
  --transition: all 0.2s ease-in-out;
}

/* Dark Mode */
.dark {
  --primary: #34d399;
  --primary-dark: #10b981;
  --primary-light: #064e3b;
  --primary-50: #064e3b;
  --primary-100: #065f46;
  --primary-200: #047857;
  --primary-300: #059669;
  --primary-400: #10b981;
  --primary-500: #34d399;
  --primary-600: #6ee7b7;
  --primary-700: #a7f3d0;
  --primary-800: #d1fae5;
  --primary-900: #ecfdf5;
  
  --gray-50: #111827;
  --gray-100: #1f2937;
  --gray-200: #374151;
  --gray-300: #4b5563;
  --gray-400: #6b7280;
  --gray-500: #9ca3af;
  --gray-600: #d1d5db;
  --gray-700: #e5e7eb;
  --gray-800: #f3f4f6;
  --gray-900: #f9fafb;
}

/* Base Styles */
body {
  font-family: var(--font-sans);
  color: var(--gray-900);
  background-color: var(--gray-50);
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.dark body {
  color: var(--gray-100);
  background-color: var(--gray-900);
}

/* Sidebar */
.vendor-sidebar {
  width: 260px;
  background: white;
  border-right: 1px solid var(--gray-200);
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
  z-index: 50;
  min-height: 100vh;
  position: relative;
}

.dark .vendor-sidebar {
  background: var(--gray-800);
  border-right-color: var(--gray-700);
}

/* Navigation */
.nav-link {
  display: flex;
  align-items: center;
  padding: 0.75rem 1.5rem;
  color: var(--gray-600);
  text-decoration: none;
  font-weight: 500;
  border-radius: 0.375rem;
  margin: 0.25rem 0.75rem;
  transition: var(--transition);
}

.nav-link:hover, .nav-link.active {
  background-color: var(--primary-50);
  color: var(--primary-700);
}

.dark .nav-link:hover, 
.dark .nav-link.active {
  background-color: var(--primary-900);
  color: var(--primary-300);
}

.nav-link i {
  margin-right: 0.75rem;
  font-size: 1.25rem;
  width: 1.25rem;
  text-align: center;
}

/* Main Content */
.main-content {
  flex: 1;
  padding: 1.5rem;
  overflow-y: auto;
  background-color: var(--gray-50);
}

.dark .main-content {
  background-color: var(--gray-900);
}

/* Cards */
.card {
  background: white;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow);
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  transition: transform 0.2s, box-shadow 0.2s;
}

.dark .card {
  background: var(--gray-800);
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.25), 0 1px 2px 0 rgba(0, 0, 0, 0.2);
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.25rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid var(--gray-200);
}

.dark .card-header {
  border-bottom-color: var(--gray-700);
}

.card-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--gray-900);
  margin: 0;
}

.dark .card-title {
  color: white;
}

/* Stats Cards */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: white;
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  box-shadow: var(--shadow);
  transition: transform 0.2s, box-shadow 0.2s;
  border-left: 4px solid var(--primary);
}

.dark .stat-card {
  background: var(--gray-800);
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.25);
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.stat-card.primary {
  border-left-color: var(--primary);
}

.stat-card.success {
  border-left-color: var(--success);
}

.stat-card.warning {
  border-left-color: var(--warning);
}

.stat-card.danger {
  border-left-color: var(--danger);
}

.stat-card-title {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--gray-500);
  margin-bottom: 0.5rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.stat-card-value {
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--gray-900);
  margin-bottom: 0.5rem;
  line-height: 1.2;
}

.dark .stat-card-value {
  color: white;
}

.stat-card-change {
  display: inline-flex;
  align-items: center;
  font-size: 0.75rem;
  font-weight: 500;
  padding: 0.125rem 0.5rem;
  border-radius: var(--radius-full);
}

.stat-card-change.positive {
  background-color: var(--success-light);
  color: var(--success);
}

.stat-card-change.negative {
  background-color: var(--danger-light);
  color: var(--danger);
}

/* Tables */
.table-container {
  background: white;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow);
  overflow: hidden;
}

.dark .table-container {
  background: var(--gray-800);
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.25);
}

table {
  width: 100%;
  border-collapse: collapse;
}

th, td {
  padding: 1rem 1.5rem;
  text-align: left;
  border-bottom: 1px solid var(--gray-200);
}

dark th,
dark td {
  border-bottom-color: var(--gray-700);
}

th {
  background-color: var(--gray-50);
  color: var(--gray-600);
  font-weight: 600;
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

dark th {
  background-color: var(--gray-700);
  color: var(--gray-300);
}

tbody tr:last-child td {
  border-bottom: none;
}

tbody tr {
  transition: background-color 0.2s;
}

tbody tr:hover {
  background-color: var(--gray-50);
}

dark tbody tr:hover {
  background-color: var(--gray-700);
}

/* Status Badges */
.badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  border-radius: var(--radius-full);
  font-size: 0.75rem;
  font-weight: 500;
  line-height: 1.25rem;
}

.badge-success {
  background-color: var(--success-light);
  color: var(--success);
}

.badge-warning {
  background-color: var(--warning-light);
  color: var(--warning);
}

.badge-danger {
  background-color: var(--danger-light);
  color: var(--danger);
}

.badge-info {
  background-color: var(--info-light);
  color: var(--info);
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1rem;
  border-radius: var(--radius);
  font-weight: 500;
  font-size: 0.875rem;
  line-height: 1.25rem;
  transition: all 0.2s;
  cursor: pointer;
  border: 1px solid transparent;
}

.btn-primary {
  background-color: var(--primary);
  color: white;
  border-color: var(--primary);
}

.btn-primary:hover {
  background-color: var(--primary-600);
  border-color: var(--primary-600);
}

.btn-outline {
  background-color: transparent;
  color: var(--primary);
  border-color: var(--primary);
}

.btn-outline:hover {
  background-color: var(--primary-50);
  color: var(--primary-700);
}

dark .btn-outline:hover {
  background-color: var(--primary-900);
  color: var(--primary-300);
}

/* Forms */
.form-group {
  margin-bottom: 1.25rem;
}

.form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--gray-700);
  font-size: 0.875rem;
}

dark .form-label {
  color: var(--gray-300);
}

.form-input {
  display: block;
  width: 100%;
  padding: 0.5rem 0.75rem;
  border: 1px solid var(--gray-300);
  border-radius: var(--radius);
  background-color: white;
  color: var(--gray-900);
  font-size: 0.875rem;
  line-height: 1.5;
  transition: border-color 0.2s, box-shadow 0.2s;
}

dark .form-input {
  background-color: var(--gray-800);
  border-color: var(--gray-700);
  color: white;
}

.form-input:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.2);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .vendor-sidebar {
    position: fixed;
    left: 0;
    top: 0;
    bottom: 0;
    transform: translateX(-100%);
    z-index: 50;
  }
  
  .vendor-sidebar.open {
    transform: translateX(0);
  }
  
  .main-content {
    margin-left: 0;
    width: 100%;
  }
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
  from { transform: translateX(-20px); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-out forwards;
}

.animate-slide-in {
  animation: slideIn 0.3s ease-out forwards;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--gray-100);
  border-radius: 3px;
}

dark ::-webkit-scrollbar-track {
  background: var(--gray-800);
}

::-webkit-scrollbar-thumb {
  background: var(--gray-300);
  border-radius: 3px;
}

dark ::-webkit-scrollbar-thumb {
  background: var(--gray-600);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--gray-400);
}

/* Loading Spinner */
.spinner {
  width: 1.5rem;
  height: 1.5rem;
  border: 2px solid var(--gray-200);
  border-top-color: var(--primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  display: inline-block;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Empty State */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 1.5rem;
  text-align: center;
  color: var(--gray-500);
}

.empty-state-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  color: var(--gray-300);
}

.empty-state-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: var(--gray-700);
}

.dark .empty-state-title {
  color: var(--gray-300);
}

.empty-state-description {
  max-width: 28rem;
  margin-bottom: 1.5rem;
  font-size: 0.9375rem;
  line-height: 1.5;
}

/* Sidebar User */
.sidebar-user-role {
    font-size: 0.9rem;
    color: #7ED957;
    font-weight: 500;
}
.modern-main-content {
    margin-left: 250px;
    min-height: 100vh;
    background: transparent;
    display: flex;
    flex-direction: column;
    transition: margin-left 0.3s cubic-bezier(0.4,0,0.2,1);
}
@media (max-width: 900px) {
    .modern-main-content {
        margin-left: 0;
    }
}
.modern-header {
    background: #fff;
    border-bottom: 1px solid #E2E8F0;
    padding: 24px 36px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: sticky;
    top: 0;
    z-index: 5;
}
.header-left {
    display: flex;
    align-items: center;
    gap: 18px;
}
.sidebar-toggle-btn {
    background: none;
    border: none;
    color: #475569;
    font-size: 1.3rem;
    cursor: pointer;
    border-radius: 8px;
    padding: 8px 10px;
    margin-right: 8px;
    transition: background 0.2s, color 0.2s;
    display: none;
}
@media (max-width: 900px) {
    .sidebar-toggle-btn {
        display: inline-flex;
    }
}
.header-title {
    font-size: 2rem;
    font-weight: 700;
    color: #1E293B;
    margin: 0;
}
.header-right {
    display: flex;
    align-items: center;
    gap: 16px;
}
.header-action-btn, .notification-btn {
    background: none;
    border: none;
    color: #475569;
    font-size: 1.2rem;
    cursor: pointer;
    position: relative;
    transition: color 0.2s, background 0.2s;
    border-radius: 8px;
    padding: 8px 10px;
}
.header-action-btn:hover, .notification-btn:hover {
    background: #F8FFF5;
    color: #388E3C;
}
.notification-badge {
    position: absolute;
    top: 2px; right: 2px;
    background: #EF4444;
    color: #fff;
    font-size: 10px;
    font-weight: 700;
    padding: 2px 6px;
    border-radius: 10px;
}
.header-profile {
    display: flex;
    align-items: center;
    gap: 10px;
    background: #F8FFF5;
    padding: 6px 14px 6px 6px;
    border-radius: 20px;
}
.profile-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
}
.profile-name {
    font-size: 1rem;
    font-weight: 600;
    color: #388E3C;
}
.modern-dashboard-content {
    padding: 36px 36px 0 36px;
    background: transparent;
}
.dashboard-hero-welcome {
    background: linear-gradient(135deg, #7ED957 0%, #4CAF50 100%);
    border-radius: 18px;
    color: #fff;
    margin-bottom: 36px;
    box-shadow: 0 4px 24px rgba(126,217,87,0.10);
    padding: 38px 32px 32px 32px;
}
.hero-welcome-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 24px;
}
.welcome-badge {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: rgba(255,255,255,0.15);
    color: #fff;
    font-size: 1rem;
    font-weight: 600;
    border-radius: 16px;
    padding: 6px 18px;
    margin-bottom: 12px;
}
.hero-title {
    font-size: 2.2rem;
    font-weight: 800;
    margin: 0 0 10px 0;
    line-height: 1.2;
}
.highlight-name {
    background: linear-gradient(135deg, #fff, #A8E584 80%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}
.hero-subtitle {
    font-size: 1.1rem;
    color: #F1F5F9;
    margin: 0;
    max-width: 500px;
    opacity: 0.95;
}
.hero-img {
    max-width: 180px;
    filter: drop-shadow(0 2px 8px rgba(0,0,0,0.08));
}
.quick-stats-preview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 22px;
    margin-bottom: 36px;
}
.quick-stat {
    display: flex;
    align-items: center;
    gap: 16px;
    background: #fff;
    border-radius: 14px;
    box-shadow: 0 2px 12px rgba(126,217,87,0.08);
    padding: 20px 18px;
    transition: box-shadow 0.2s;
}
.quick-stat:hover {
    box-shadow: 0 6px 24px rgba(126,217,87,0.16);
}
.stat-icon {
    width: 44px;
    height: 44px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: #fff;
}
.stat-icon.revenue { background: linear-gradient(135deg, #FF6B35, #FF8A65); }
.stat-icon.orders { background: linear-gradient(135deg, #7ED957, #4CAF50); }
.stat-icon.customers { background: linear-gradient(135deg, #6366F1, #818CF8); }
.stat-icon.products { background: linear-gradient(135deg, #BA68C8, #7ED957); }
.stat-info {
    display: flex;
    flex-direction: column;
}
.stat-value {
    font-size: 1.3rem;
    font-weight: 700;
    color: #1E293B;
}
.stat-label {
    font-size: 0.98rem;
    color: #64748B;
    margin-top: 2px;
}
.modern-orders-container {
    background: #fff;
    border-radius: 14px;
    box-shadow: 0 2px 12px rgba(126,217,87,0.08);
    padding: 28px 20px 18px 20px;
    margin-bottom: 36px;
}
.orders-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 18px;
}
.orders-header h2 {
    font-size: 1.3rem;
    font-weight: 600;
    color: #333;
    margin: 0;
}
.view-all-btn {
    color: #7ED957;
    text-decoration: none;
    font-weight: 500;
    font-size: 1rem;
    transition: color 0.2s;
}
.view-all-btn:hover { color: #388E3C; }
.orders-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 1rem;
}
.orders-table th, .orders-table td {
    padding: 12px 0;
    text-align: left;
}
.orders-table th {
    color: #666;
    font-weight: 500;
    border-bottom: 2px solid #E5E7EB;
}
.orders-table tr {
    border-bottom: 1px solid #E5E7EB;
}
.modern-status-badge {
    display: inline-block;
    padding: 6px 16px;
    border-radius: 24px;
    font-size: 0.95rem;
    font-weight: 500;
}
.modern-status-badge.completed {
    background: #E8FFE1;
    color: #3EA045;
}
.modern-status-badge.processing {
    background: #FFF8E1;
    color: #FF9800;
}
.modern-status-badge.shipped {
    background: #E1F5FE;
    color: #0288D1;
}
@media (max-width: 1100px) {
    .modern-main-content { margin-left: 0; }
    .modern-vendor-dashboard { flex-direction: column; }
    .modern-sidebar { position: relative; width: 100%; min-height: auto; border-right: none; flex-direction: row; justify-content: space-between; padding: 16px 0; }
    .sidebar-nav { flex-direction: row; gap: 8px; }
    .sidebar-link { padding: 10px 16px; font-size: 0.98rem; }
    .sidebar-user { flex-direction: row; gap: 12px; padding: 12px 0; }
}
@media (max-width: 700px) {
    .modern-header, .modern-dashboard-content { padding: 12px 4vw; }
    .dashboard-hero-welcome { padding: 18px 8px; }
    .hero-title { font-size: 1.2rem; }
    .quick-stats-preview { gap: 10px; }
    .modern-orders-container { padding: 10px 2px; }
}
@media (max-width: 500px) {
    .sidebar-brand-title { display: none; }
    .modern-header { flex-direction: column; gap: 8px; }
    .header-title { font-size: 1.1rem; }
    .profile-name { display: none; }
}

/* Modern Vendor Dashboard Styles - Enhanced UI/UX */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Poppins:wght@300;400;500;600;700;800&display=swap');

:root {
    /* Enhanced Color Palette */
    --whamart-primary: #7ED957;
    --whamart-primary-dark: #5DC264;
    --whamart-primary-light: #A8E584;
    --whamart-secondary: #4CAF50;
    --whamart-secondary-dark: #3EA045;
    --whamart-accent: #FF6B35;
    --whamart-accent-light: #FF8A65;
    --whamart-info: #6366F1;
    --whamart-info-light: #818CF8;
    --whamart-success: #10B981;
    --whamart-warning: #F59E0B;
    --whamart-error: #EF4444;
    
    /* Background Colors */
    --whamart-bg-primary: #FFFFFF;
    --whamart-bg-secondary: #F8FAFC;
    --whamart-bg-tertiary: #F1F5F9;
    --whamart-bg-accent: #F8FFF5;
    --whamart-bg-gradient: linear-gradient(135deg, #F8FAFC 0%, #E2E8F0 100%);
    
    /* Text Colors */
    --whamart-text-primary: #1E293B;
    --whamart-text-secondary: #475569;
    --whamart-text-tertiary: #64748B;
    --whamart-text-muted: #94A3B8;
    --whamart-text-light: #CBD5E1;
    
    /* Border & Shadow */
    --whamart-border: #E2E8F0;
    --whamart-border-light: #F1F5F9;
    --whamart-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --whamart-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --whamart-shadow-md: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --whamart-shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --whamart-shadow-xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    
    /* Border Radius */
    --whamart-radius-sm: 6px;
    --whamart-radius: 12px;
    --whamart-radius-md: 16px;
    --whamart-radius-lg: 20px;
    --whamart-radius-xl: 24px;
    
    /* Transitions */
    --whamart-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --whamart-transition-fast: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
    --whamart-transition-slow: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    
    /* Spacing */
    --whamart-spacing-xs: 4px;
    --whamart-spacing-sm: 8px;
    --whamart-spacing: 16px;
    --whamart-spacing-md: 24px;
    --whamart-spacing-lg: 32px;
    --whamart-spacing-xl: 48px;
    --whamart-spacing-2xl: 64px;
}

/* Modern Dashboard Layout */
.modern-vendor-dashboard {
    min-height: 100vh;
    background: var(--whamart-bg-gradient);
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    color: var(--whamart-text-primary);
    line-height: 1.6;
}

/* Loading State */
.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 60vh;
    gap: var(--whamart-spacing-md);
}

.loading-spinner {
    position: relative;
    width: 80px;
    height: 80px;
}

.spinner-ring {
    position: absolute;
    width: 100%;
    height: 100%;
    border: 3px solid transparent;
    border-top: 3px solid var(--whamart-primary);
    border-radius: 50%;
    animation: spin 1.2s cubic-bezier(0.5, 0, 0.5, 1) infinite;
}

.spinner-ring:nth-child(1) { animation-delay: -0.45s; }
.spinner-ring:nth-child(2) { animation-delay: -0.3s; }
.spinner-ring:nth-child(3) { animation-delay: -0.15s; }

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    font-size: 16px;
    font-weight: 500;
    color: var(--whamart-text-secondary);
    margin: 0;
}

/* Dashboard Content */
.dashboard-content {
    padding: var(--whamart-spacing-lg);
    max-width: 1400px;
    margin: 0 auto;
    animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Hero Welcome Section */
.hero-welcome-section {
    position: relative;
    background: var(--whamart-bg-primary);
    border-radius: var(--whamart-radius-xl);
    padding: var(--whamart-spacing-2xl);
    margin-bottom: var(--whamart-spacing-xl);
    overflow: hidden;
    box-shadow: var(--whamart-shadow-lg);
    border: 1px solid var(--whamart-border);
}

.hero-background {
    position: absolute;
    inset: 0;
    z-index: 0;
}

.hero-pattern {
    position: absolute;
    inset: 0;
    background-image: 
        radial-gradient(circle at 25% 25%, rgba(126, 217, 87, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(76, 175, 80, 0.1) 0%, transparent 50%);
}

.hero-gradient {
    position: absolute;
    inset: 0;
    background: linear-gradient(135deg, rgba(126, 217, 87, 0.05) 0%, rgba(76, 175, 80, 0.05) 100%);
}

.hero-content {
    position: relative;
    z-index: 1;
}

.welcome-header {
    display: flex;
    align-items: center;
    gap: var(--whamart-spacing-md);
    margin-bottom: var(--whamart-spacing-xl);
}

.welcome-avatar {
    position: relative;
    flex-shrink: 0;
}

.avatar-img {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    border: 4px solid var(--whamart-bg-primary);
    box-shadow: var(--whamart-shadow-md);
}

.avatar-status {
    position: absolute;
    bottom: 4px;
    right: 4px;
    width: 20px;
    height: 20px;
    background: var(--whamart-success);
    border: 3px solid var(--whamart-bg-primary);
    border-radius: 50%;
}

.welcome-text {
    flex: 1;
}

.welcome-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--whamart-spacing-sm);
    padding: var(--whamart-spacing-sm) var(--whamart-spacing);
    background: linear-gradient(135deg, var(--whamart-primary), var(--whamart-secondary));
    color: white;
    border-radius: var(--whamart-radius-lg);
    font-size: 13px;
    font-weight: 600;
    margin-bottom: var(--whamart-spacing);
    box-shadow: var(--whamart-shadow);
}

.hero-title {
    font-size: clamp(24px, 4vw, 36px);
    font-weight: 800;
    color: var(--whamart-text-primary);
    margin: 0 0 var(--whamart-spacing) 0;
    line-height: 1.2;
}

.highlight-name {
    background: linear-gradient(135deg, var(--whamart-primary), var(--whamart-secondary));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 18px;
    color: var(--whamart-text-secondary);
    margin: 0;
    max-width: 600px;
}

/* Quick Stats Preview */
.quick-stats-preview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--whamart-spacing-md);
}

.quick-stat {
    display: flex;
    align-items: center;
    gap: var(--whamart-spacing);
    padding: var(--whamart-spacing-md);
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    border-radius: var(--whamart-radius-md);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: var(--whamart-shadow);
}

.quick-stat .stat-icon {
    width: 48px;
    height: 48px;
    border-radius: var(--whamart-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    flex-shrink: 0;
}

.quick-stat .stat-icon.revenue {
    background: linear-gradient(135deg, #FF6B35, #FF8A65);
    color: white;
}

.quick-stat .stat-icon.orders {
    background: linear-gradient(135deg, var(--whamart-primary), var(--whamart-secondary));
    color: white;
}

.quick-stat .stat-icon.customers {
    background: linear-gradient(135deg, var(--whamart-info), var(--whamart-info-light));
    color: white;
}

.quick-stat .stat-info {
    display: flex;
    flex-direction: column;
}

.quick-stat .stat-value {
    font-size: 20px;
    font-weight: 700;
    color: var(--whamart-text-primary);
    line-height: 1;
}

.quick-stat .stat-label {
    font-size: 13px;
    color: var(--whamart-text-tertiary);
    margin-top: 2px;
}

/* Enhanced Stats Grid */
.enhanced-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--whamart-spacing-md);
    margin-bottom: var(--whamart-spacing-xl);
}

.enhanced-stat-card {
    background: var(--whamart-bg-primary);
    border-radius: var(--whamart-radius-md);
    padding: var(--whamart-spacing-md);
    box-shadow: var(--whamart-shadow);
    border: 1px solid var(--whamart-border);
    transition: var(--whamart-transition);
    position: relative;
    overflow: hidden;
    animation: slideInUp 0.6s ease-out both;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.enhanced-stat-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--whamart-shadow-lg);
    border-color: var(--whamart-primary);
}

.enhanced-stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--whamart-primary), var(--whamart-secondary));
}

.stat-card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--whamart-spacing);
}

.stat-icon-wrapper {
    width: 56px;
    height: 56px;
    border-radius: var(--whamart-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: white;
    box-shadow: var(--whamart-shadow);
}

.stat-icon-wrapper.products {
    background: linear-gradient(135deg, var(--whamart-primary), var(--whamart-primary-dark));
}

.stat-icon-wrapper.orders {
    background: linear-gradient(135deg, var(--whamart-secondary), var(--whamart-secondary-dark));
}

.stat-icon-wrapper.revenue {
    background: linear-gradient(135deg, var(--whamart-accent), var(--whamart-accent-light));
}

.stat-icon-wrapper.customers {
    background: linear-gradient(135deg, var(--whamart-info), var(--whamart-info-light));
}

.stat-trend {
    display: flex;
    align-items: center;
    gap: var(--whamart-spacing-xs);
    padding: var(--whamart-spacing-xs) var(--whamart-spacing-sm);
    border-radius: var(--whamart-radius-sm);
    font-size: 12px;
    font-weight: 600;
}

.stat-trend.positive {
    background: rgba(16, 185, 129, 0.1);
    color: var(--whamart-success);
}

.stat-trend.negative {
    background: rgba(239, 68, 68, 0.1);
    color: var(--whamart-error);
}

.stat-card-body {
    margin-bottom: var(--whamart-spacing);
}

.stat-title {
    font-size: 14px;
    font-weight: 500;
    color: var(--whamart-text-tertiary);
    margin: 0 0 var(--whamart-spacing-sm) 0;
}

.stat-value-container {
    display: flex;
    align-items: baseline;
    gap: var(--whamart-spacing-sm);
    margin-bottom: var(--whamart-spacing);
}

.stat-main-value {
    font-size: 28px;
    font-weight: 800;
    color: var(--whamart-text-primary);
    line-height: 1;
}

.stat-change-text {
    font-size: 13px;
    color: var(--whamart-text-tertiary);
}

.stat-progress-bar {
    width: 100%;
    height: 6px;
    background: var(--whamart-bg-tertiary);
    border-radius: 3px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    border-radius: 3px;
    transition: width 0.8s ease-out;
}

.stat-card-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-top: var(--whamart-spacing);
    border-top: 1px solid var(--whamart-border-light);
}

.stat-period {
    font-size: 12px;
    color: var(--whamart-text-muted);
}

.stat-details-btn {
    background: none;
    border: none;
    color: var(--whamart-primary);
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    padding: var(--whamart-spacing-xs) var(--whamart-spacing-sm);
    border-radius: var(--whamart-radius-sm);
    transition: var(--whamart-transition-fast);
}

.stat-details-btn:hover {
    background: rgba(126, 217, 87, 0.1);
}

/* Dashboard Sections */
.dashboard-section {
    margin-bottom: var(--whamart-spacing-xl);
}

.section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--whamart-spacing-md);
    flex-wrap: wrap;
    gap: var(--whamart-spacing);
}

.section-title-group {
    display: flex;
    flex-direction: column;
    gap: var(--whamart-spacing-xs);
}

.section-title {
    display: flex;
    align-items: center;
    gap: var(--whamart-spacing-sm);
    font-size: 24px;
    font-weight: 700;
    color: var(--whamart-text-primary);
    margin: 0;
}

.section-icon {
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--whamart-primary), var(--whamart-secondary));
    color: white;
    border-radius: var(--whamart-radius-sm);
    font-size: 14px;
}

.section-subtitle {
    font-size: 14px;
    color: var(--whamart-text-tertiary);
    margin: 0;
}

.section-actions {
    display: flex;
    align-items: center;
    gap: var(--whamart-spacing-sm);
}

.filter-btn {
    display: flex;
    align-items: center;
    gap: var(--whamart-spacing-sm);
    padding: var(--whamart-spacing-sm) var(--whamart-spacing);
    background: var(--whamart-bg-primary);
    border: 1px solid var(--whamart-border);
    border-radius: var(--whamart-radius-sm);
    color: var(--whamart-text-secondary);
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: var(--whamart-transition-fast);
}

.filter-btn:hover {
    background: var(--whamart-bg-secondary);
    border-color: var(--whamart-primary);
}

.view-all-btn {
    display: flex;
    align-items: center;
    gap: var(--whamart-spacing-sm);
    padding: var(--whamart-spacing-sm) var(--whamart-spacing);
    background: linear-gradient(135deg, var(--whamart-primary), var(--whamart-secondary));
    color: white;
    text-decoration: none;
    border-radius: var(--whamart-radius-sm);
    font-size: 14px;
    font-weight: 600;
    transition: var(--whamart-transition-fast);
    box-shadow: var(--whamart-shadow);
}

.view-all-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--whamart-shadow-md);
}

/* Modern Orders Container */
.modern-orders-container {
    display: grid;
    gap: var(--whamart-spacing);
}

.modern-order-card {
    background: var(--whamart-bg-primary);
    border-radius: var(--whamart-radius-md);
    padding: var(--whamart-spacing-md);
    box-shadow: var(--whamart-shadow);
    border: 1px solid var(--whamart-border);
    transition: var(--whamart-transition);
    animation: slideInUp 0.6s ease-out both;
}

.modern-order-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--whamart-shadow-md);
    border-color: var(--whamart-primary);
}

.order-card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--whamart-spacing);
    padding-bottom: var(--whamart-spacing);
    border-bottom: 1px solid var(--whamart-border-light);
}

.order-id-section {
    display: flex;
    flex-direction: column;
    gap: var(--whamart-spacing-xs);
}

.order-id {
    font-size: 16px;
    font-weight: 700;
    color: var(--whamart-text-primary);
}

.order-time {
    font-size: 12px;
    color: var(--whamart-text-muted);
}

.modern-status-badge {
    padding: var(--whamart-spacing-xs) var(--whamart-spacing-sm);
    border-radius: var(--whamart-radius-sm);
    font-size: 11px;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.modern-status-badge.completed {
    background: rgba(16, 185, 129, 0.1);
    color: var(--whamart-success);
    border: 1px solid rgba(16, 185, 129, 0.2);
}

.modern-status-badge.pending {
    background: rgba(245, 158, 11, 0.1);
    color: var(--whamart-warning);
    border: 1px solid rgba(245, 158, 11, 0.2);
}

.modern-status-badge.processing {
    background: rgba(99, 102, 241, 0.1);
    color: var(--whamart-info);
    border: 1px solid rgba(99, 102, 241, 0.2);
}

.modern-status-badge.cancelled {
    background: rgba(239, 68, 68, 0.1);
    color: var(--whamart-error);
    border: 1px solid rgba(239, 68, 68, 0.2);
}

.modern-status-badge.shipped {
    background: rgba(126, 217, 87, 0.1);
    color: var(--whamart-primary);
    border: 1px solid rgba(126, 217, 87, 0.2);
}

.order-card-body {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--whamart-spacing);
}

.customer-info {
    display: flex;
    align-items: center;
    gap: var(--whamart-spacing);
}

.customer-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
    flex-shrink: 0;
}

.avatar-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.customer-details {
    display: flex;
    flex-direction: column;
    gap: var(--whamart-spacing-xs);
}

.customer-name {
    font-size: 14px;
    font-weight: 600;
    color: var(--whamart-text-primary);
}

.customer-type {
    font-size: 11px;
    color: var(--whamart-text-muted);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.order-amount {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: var(--whamart-spacing-xs);
}

.amount-value {
    font-size: 18px;
    font-weight: 700;
    color: var(--whamart-text-primary);
}

.amount-label {
    font-size: 11px;
    color: var(--whamart-text-muted);
}

.order-card-footer {
    padding-top: var(--whamart-spacing);
    border-top: 1px solid var(--whamart-border-light);
}

.order-actions {
    display: flex;
    gap: var(--whamart-spacing-sm);
}

.action-btn {
    padding: var(--whamart-spacing-sm) var(--whamart-spacing);
    border-radius: var(--whamart-radius-sm);
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--whamart-transition-fast);
    border: none;
}

.view-btn {
    background: var(--whamart-bg-secondary);
    color: var(--whamart-text-secondary);
    border: 1px solid var(--whamart-border);
}

.view-btn:hover {
    background: var(--whamart-bg-tertiary);
    border-color: var(--whamart-primary);
}

.process-btn {
    background: linear-gradient(135deg, var(--whamart-primary), var(--whamart-secondary));
    color: white;
}

.process-btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--whamart-shadow);
}

/* Enhanced Quick Actions */
.enhanced-quick-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--whamart-spacing-md);
}

.enhanced-action-card {
    position: relative;
    background: var(--whamart-bg-primary);
    border-radius: var(--whamart-radius-md);
    padding: var(--whamart-spacing-md);
    text-decoration: none;
    color: inherit;
    transition: var(--whamart-transition);
    overflow: hidden;
    border: 1px solid var(--whamart-border);
    box-shadow: var(--whamart-shadow);
}

.enhanced-action-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--whamart-shadow-lg);
}

.action-card-background {
    position: absolute;
    inset: 0;
    opacity: 0;
    transition: var(--whamart-transition);
}

.enhanced-action-card:hover .action-card-background {
    opacity: 1;
}

.enhanced-action-card.primary .action-card-background {
    background: linear-gradient(135deg, rgba(126, 217, 87, 0.05), rgba(92, 194, 100, 0.05));
}

.enhanced-action-card.secondary .action-card-background {
    background: linear-gradient(135deg, rgba(76, 175, 80, 0.05), rgba(62, 160, 69, 0.05));
}

.enhanced-action-card.accent .action-card-background {
    background: linear-gradient(135deg, rgba(255, 107, 53, 0.05), rgba(255, 138, 101, 0.05));
}

.enhanced-action-card.info .action-card-background {
    background: linear-gradient(135deg, rgba(99, 102, 241, 0.05), rgba(129, 140, 248, 0.05));
}

.action-pattern {
    position: absolute;
    inset: 0;
    background-image: 
        radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
}

.action-card-content {
    position: relative;
    z-index: 1;
    display: flex;
    align-items: center;
    gap: var(--whamart-spacing);
}

.action-icon-container {
    width: 56px;
    height: 56px;
    border-radius: var(--whamart-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: white;
    flex-shrink: 0;
    box-shadow: var(--whamart-shadow);
}

.enhanced-action-card.primary .action-icon-container {
    background: linear-gradient(135deg, var(--whamart-primary), var(--whamart-primary-dark));
}

.enhanced-action-card.secondary .action-icon-container {
    background: linear-gradient(135deg, var(--whamart-secondary), var(--whamart-secondary-dark));
}

.enhanced-action-card.accent .action-icon-container {
    background: linear-gradient(135deg, var(--whamart-accent), var(--whamart-accent-light));
}

.enhanced-action-card.info .action-icon-container {
    background: linear-gradient(135deg, var(--whamart-info), var(--whamart-info-light));
}

.action-text {
    flex: 1;
}

.action-title {
    font-size: 18px;
    font-weight: 700;
    color: var(--whamart-text-primary);
    margin: 0 0 var(--whamart-spacing-xs) 0;
}

.action-description {
    font-size: 14px;
    color: var(--whamart-text-tertiary);
    margin: 0;
}

.action-arrow {
    color: var(--whamart-text-muted);
    transition: var(--whamart-transition-fast);
}

.enhanced-action-card:hover .action-arrow {
    color: var(--whamart-primary);
    transform: translateX(4px);
}

/* Performance Insights */
.insights-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--whamart-spacing-md);
}

.insight-card {
    background: var(--whamart-bg-primary);
    border-radius: var(--whamart-radius-md);
    padding: var(--whamart-spacing-md);
    box-shadow: var(--whamart-shadow);
    border: 1px solid var(--whamart-border);
}

.insight-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--whamart-spacing);
    padding-bottom: var(--whamart-spacing);
    border-bottom: 1px solid var(--whamart-border-light);
}

.insight-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--whamart-text-primary);
    margin: 0;
}

.insight-period {
    font-size: 12px;
    color: var(--whamart-text-muted);
    background: var(--whamart-bg-secondary);
    padding: var(--whamart-spacing-xs) var(--whamart-spacing-sm);
    border-radius: var(--whamart-radius-sm);
}

.product-list {
    display: flex;
    flex-direction: column;
    gap: var(--whamart-spacing);
}

.product-item {
    display: flex;
    flex-direction: column;
    gap: var(--whamart-spacing-sm);
}

.product-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.product-name {
    font-size: 14px;
    font-weight: 500;
    color: var(--whamart-text-primary);
}

.product-sales {
    font-size: 12px;
    color: var(--whamart-text-tertiary);
}

.product-progress {
    width: 100%;
}

.progress-bar {
    width: 100%;
    height: 6px;
    background: var(--whamart-bg-tertiary);
    border-radius: 3px;
    overflow: hidden;
}

.progress-bar .progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--whamart-primary), var(--whamart-secondary));
    border-radius: 3px;
    transition: width 0.8s ease-out;
}

/* Revenue Chart */
.revenue-chart {
    display: flex;
    flex-direction: column;
    gap: var(--whamart-spacing);
}

.chart-bars {
    display: flex;
    align-items: end;
    gap: var(--whamart-spacing-sm);
    height: 120px;
    padding: var(--whamart-spacing);
    background: var(--whamart-bg-secondary);
    border-radius: var(--whamart-radius-sm);
}

.chart-bar {
    flex: 1;
    background: linear-gradient(to top, var(--whamart-primary), var(--whamart-primary-light));
    border-radius: 3px 3px 0 0;
    min-height: 20px;
    transition: all 0.8s ease-out;
}

.chart-labels {
    display: flex;
    justify-content: space-between;
    font-size: 11px;
    color: var(--whamart-text-muted);
    padding: 0 var(--whamart-spacing);
}

/* Responsive Design */
@media (max-width: 768px) {
    .dashboard-content {
        padding: var(--whamart-spacing);
    }
    
    .hero-welcome-section {
        padding: var(--whamart-spacing-md);
    }
    
    .welcome-header {
        flex-direction: column;
        text-align: center;
        gap: var(--whamart-spacing);
    }
    
    .hero-title {
        font-size: 24px;
    }
    
    .quick-stats-preview {
        grid-template-columns: 1fr;
    }
    
    .enhanced-stats-grid {
        grid-template-columns: 1fr;
    }
    
    .enhanced-quick-actions {
        grid-template-columns: 1fr;
    }
    
    .insights-grid {
        grid-template-columns: 1fr;
    }
    
    .section-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .order-card-body {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--whamart-spacing);
    }
    
    .order-amount {
        align-items: flex-start;
    }
}

@media (max-width: 480px) {
    .hero-welcome-section {
        padding: var(--whamart-spacing);
    }
    
    .welcome-badge {
        font-size: 11px;
        padding: var(--whamart-spacing-xs) var(--whamart-spacing-sm);
    }
    
    .hero-title {
        font-size: 20px;
    }
    
    .hero-subtitle {
        font-size: 14px;
    }
    
    .section-title {
        font-size: 20px;
    }
    
    .enhanced-stat-card {
        padding: var(--whamart-spacing);
    }
    
    .modern-order-card {
        padding: var(--whamart-spacing);
    }
}

/* Animation delays for staggered effects */
.enhanced-stat-card:nth-child(1) { animation-delay: 0.1s; }
.enhanced-stat-card:nth-child(2) { animation-delay: 0.2s; }
.enhanced-stat-card:nth-child(3) { animation-delay: 0.3s; }
.enhanced-stat-card:nth-child(4) { animation-delay: 0.4s; }

.modern-order-card:nth-child(1) { animation-delay: 0.1s; }
.modern-order-card:nth-child(2) { animation-delay: 0.2s; }
.modern-order-card:nth-child(3) { animation-delay: 0.3s; }
.modern-order-card:nth-child(4) { animation-delay: 0.4s; }
.modern-order-card:nth-child(5) { animation-delay: 0.5s; }