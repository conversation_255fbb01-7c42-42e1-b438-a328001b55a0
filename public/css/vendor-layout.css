/* --- Vendor Dashboard Modern Layout --- */
:root {
    --vendor-green: #7ED957;
    --vendor-green-dark: #4CAF50;
    --vendor-bg: #f9fafb;
    --vendor-sidebar-bg: #fff;
    --vendor-header-bg: #fff;
    --vendor-border: #e5e7eb;
    --vendor-text: #222;
    --vendor-muted: #6b7280;
    --vendor-accent: #10b981;
    --vendor-shadow: 0 2px 12px rgba(126,217,87,0.07);
}
body.vendor-dashboard {
    font-family: 'Inter', 'Poppins', Arial, sans-serif;
    background: var(--vendor-bg);
    color: var(--vendor-text);
    margin: 0;
    min-height: 100vh;
}
.vendor-layout {
    display: flex;
    min-height: 100vh;
    background: var(--vendor-bg);
}
/* Sidebar */
.vendor-sidebar {
    width: 250px;
    background: var(--vendor-sidebar-bg);
    border-right: 1px solid var(--vendor-border);
    display: flex;
    flex-direction: column;
    transition: transform 0.3s;
    z-index: 100;
    min-height: 100vh;
    position: relative;
}
.vendor-sidebar .sidebar-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 24px 20px 16px 20px;
    border-bottom: 1px solid var(--vendor-border);
}
.vendor-sidebar .sidebar-logo {
    display: flex;
    align-items: center;
    gap: 10px;
}
.vendor-sidebar .sidebar-logo-img {
    height: 32px;
    width: auto;
    border-radius: 8px;
}
.vendor-sidebar .sidebar-logo-fallback {
    width: 32px;
    height: 32px;
    background: linear-gradient(135deg, var(--vendor-green), var(--vendor-green-dark));
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 1.2rem;
    border-radius: 8px;
}
.vendor-sidebar .sidebar-title {
    font-weight: 700;
    font-size: 1.2rem;
    color: var(--vendor-green-dark);
}
.vendor-sidebar nav {
    flex: 1;
    padding: 24px 0 0 0;
}
.vendor-sidebar .nav-section {
    margin-bottom: 24px;
}
.vendor-sidebar .nav-section-title {
    font-size: 0.8rem;
    color: var(--vendor-muted);
    text-transform: uppercase;
    margin: 0 0 10px 24px;
    letter-spacing: 1px;
}
.vendor-sidebar .nav-link {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 24px;
    color: var(--vendor-text);
    text-decoration: none;
    border-radius: 8px;
    font-weight: 500;
    transition: background 0.2s, color 0.2s;
    position: relative;
}
.vendor-sidebar .nav-link.active,
.vendor-sidebar .nav-link:hover {
    background: var(--vendor-green);
    color: #fff;
}
.vendor-sidebar .nav-link .fa {
    font-size: 1.1rem;
}
.vendor-sidebar .profile-section {
    margin-top: auto;
    padding: 20px 24px;
    border-top: 1px solid var(--vendor-border);
}
.vendor-sidebar .profile-info {
    display: flex;
    align-items: center;
    gap: 12px;
}
.vendor-sidebar .profile-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--vendor-green);
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    font-weight: 700;
}
.vendor-sidebar .profile-details {
    display: flex;
    flex-direction: column;
}
.vendor-sidebar .profile-name {
    font-weight: 600;
    font-size: 1rem;
}
.vendor-sidebar .profile-role {
    font-size: 0.85rem;
    color: var(--vendor-muted);
}
.vendor-sidebar .profile-actions {
    margin-top: 12px;
    display: flex;
    flex-direction: column;
    gap: 6px;
}
.vendor-sidebar .profile-action {
    color: var(--vendor-muted);
    font-size: 0.95rem;
    text-decoration: none;
    padding: 6px 0;
    transition: color 0.2s;
}
.vendor-sidebar .profile-action:hover {
    color: var(--vendor-green-dark);
}
/* Sidebar mobile */
@media (max-width: 900px) {
    .vendor-sidebar {
        position: fixed;
        left: 0;
        top: 0;
        height: 100vh;
        transform: translateX(-100%);
        box-shadow: 2px 0 16px rgba(0,0,0,0.07);
        transition: transform 0.3s;
        width: 220px;
    }
    .vendor-sidebar.open {
        transform: translateX(0);
    }
    .vendor-layout {
        flex-direction: column;
    }
}
/* Overlay for mobile sidebar */
.vendor-sidebar-overlay {
    display: none;
    position: fixed;
    top: 0; left: 0; right: 0; bottom: 0;
    background: rgba(0,0,0,0.2);
    z-index: 99;
}
.vendor-sidebar.open ~ .vendor-sidebar-overlay {
    display: block;
}
/* Header */
.vendor-header {
    background: var(--vendor-header-bg);
    border-bottom: 1px solid var(--vendor-border);
    padding: 0 32px;
    height: 64px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: sticky;
    top: 0;
    z-index: 50;
    box-shadow: 0 2px 8px rgba(0,0,0,0.03);
}
.vendor-header .header-left {
    display: flex;
    align-items: center;
    gap: 18px;
}
.vendor-header .header-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--vendor-green-dark);
}
.vendor-header .header-search {
    position: relative;
}
.vendor-header .header-search input {
    padding: 8px 32px 8px 12px;
    border-radius: 8px;
    border: 1px solid var(--vendor-border);
    background: #f3f4f6;
    font-size: 1rem;
    outline: none;
    transition: border 0.2s;
}
.vendor-header .header-search .fa-search {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--vendor-muted);
    font-size: 1rem;
}
.vendor-header .header-actions {
    display: flex;
    align-items: center;
    gap: 18px;
}
.vendor-header .header-action-btn {
    background: none;
    border: none;
    color: var(--vendor-muted);
    font-size: 1.2rem;
    cursor: pointer;
    position: relative;
    transition: color 0.2s;
}
.vendor-header .header-action-btn:hover {
    color: var(--vendor-green-dark);
}
.vendor-header .notification-badge {
    position: absolute;
    top: -6px;
    right: -6px;
    background: #ef4444;
    color: #fff;
    font-size: 0.7rem;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
}
.vendor-header .user-dropdown {
    position: relative;
}
.vendor-header .user-avatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background: var(--vendor-green);
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.1rem;
    font-weight: 700;
    margin-right: 8px;
}
.vendor-header .user-name {
    font-size: 1rem;
    font-weight: 500;
    color: var(--vendor-text);
}
.vendor-header .dropdown-menu {
    position: absolute;
    right: 0;
    top: 48px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 16px rgba(0,0,0,0.07);
    min-width: 180px;
    padding: 8px 0;
    z-index: 100;
    display: none;
}
.vendor-header .dropdown-menu.show {
    display: block;
}
.vendor-header .dropdown-item {
    padding: 10px 18px;
    color: var(--vendor-text);
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 1rem;
    border-radius: 6px;
    transition: background 0.2s;
}
.vendor-header .dropdown-item:hover {
    background: var(--vendor-green);
    color: #fff;
}
/* Main Content */
.vendor-main {
    flex: 1;
    padding: 32px 40px;
    min-width: 0;
}
@media (max-width: 900px) {
    .vendor-main {
        padding: 24px 10px;
    }
    .vendor-header {
        padding: 0 10px;
    }
}
/* Utility */
.card {
    background: #fff;
    border-radius: 12px;
    box-shadow: var(--vendor-shadow);
    padding: 24px;
    margin-bottom: 24px;
}
.btn {
    display: inline-block;
    padding: 10px 24px;
    border-radius: 24px;
    background: var(--vendor-green);
    color: #fff;
    font-weight: 600;
    border: none;
    cursor: pointer;
    font-size: 1rem;
    transition: background 0.2s;
    text-decoration: none;
}
.btn:hover {
    background: var(--vendor-green-dark);
}
.btn-outline {
    background: none;
    color: var(--vendor-green-dark);
    border: 2px solid var(--vendor-green-dark);
}
.btn-outline:hover {
    background: var(--vendor-green-dark);
    color: #fff;
}
/* Hide scrollbar for sidebar nav on mobile */
.vendor-sidebar nav {
    overflow-y: auto;
    max-height: calc(100vh - 200px);
} 